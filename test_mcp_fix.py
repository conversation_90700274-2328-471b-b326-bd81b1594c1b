#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP修复的简单脚本
"""

import asyncio
import logging
from mcp_universal_client import MCPMultiServerClient

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_mcp_fix():
    """测试MCP修复"""
    print("🧪 测试MCP TaskGroup错误修复")
    print("=" * 50)
    
    try:
        # 初始化MCP客户端
        print("1. 初始化MCP客户端...")
        mcp_client = MCPMultiServerClient('mcp_config.json')
        print("✅ MCP客户端初始化成功")
        
        # 测试获取工具列表
        print("\n2. 测试获取工具列表...")
        all_tools = await mcp_client.list_all_tools()
        print(f"✅ 工具列表获取成功，共 {len(all_tools)} 个服务器")
        
        for server_name, tools in all_tools.items():
            print(f"   📋 {server_name}: {len(tools)} 个工具")
        
        # 测试调用一个简单的工具
        print("\n3. 测试调用工具...")
        test_params = {
            "workspace_id": 33137843,
            "name": "测试需求-MCP修复验证",
            "description": "这是一个测试需求，用于验证MCP TaskGroup错误修复",
            "priority": "低",
            "status": "新建"
        }
        
        result = await mcp_client.find_and_call_tool("create_story_or_task", test_params)
        
        if result.get("error") or result.get("fallback_mode"):
            print(f"⚠️  工具调用返回错误（这是预期的）: {result.get('error', 'Unknown error')}")
            print("✅ 错误处理机制工作正常，没有抛出TaskGroup异常")
        else:
            print("✅ 工具调用成功")
            
        print("\n🎉 MCP修复测试完成！")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "unhandled errors in a TaskGroup" in error_msg:
            print(f"❌ TaskGroup错误仍然存在: {error_msg}")
            return False
        else:
            print(f"⚠️  其他错误（可能是正常的）: {error_msg}")
            return True

async def main():
    """主函数"""
    success = await test_mcp_fix()
    if success:
        print("\n✅ 测试通过：TaskGroup错误已修复")
    else:
        print("\n❌ 测试失败：TaskGroup错误仍然存在")

if __name__ == "__main__":
    asyncio.run(main())
