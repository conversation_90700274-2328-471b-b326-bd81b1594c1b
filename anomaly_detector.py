#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常检测模块 - 监控业务数据并识别异常事件
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import statistics
from enum import Enum


class AnomalyType(Enum):
    """异常类型枚举"""
    THRESHOLD = "threshold"          # 阈值异常
    TREND = "trend"                 # 趋势异常
    PATTERN = "pattern"             # 模式异常
    CORRELATION = "correlation"     # 关联异常


class Severity(Enum):
    """严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AnomalyEvent:
    """异常事件数据结构"""
    event_id: str
    metric_name: str
    anomaly_type: AnomalyType
    severity: Severity
    current_value: float
    expected_value: Optional[float]
    threshold: Optional[float]
    deviation_score: float
    timestamp: str
    source: str
    context: Dict[str, Any]
    description: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['anomaly_type'] = self.anomaly_type.value
        data['severity'] = self.severity.value
        return data


@dataclass
class MetricRule:
    """监控规则配置"""
    metric_name: str
    rule_type: AnomalyType
    threshold_high: Optional[float] = None
    threshold_low: Optional[float] = None
    window_size: int = 10
    sensitivity: float = 0.8
    enabled: bool = True
    context: Dict[str, Any] = None


class AnomalyDetector:
    """异常检测器"""
    
    def __init__(self, config_file: str = "anomaly_config.json"):
        """
        初始化异常检测器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 监控规则
        self.rules: Dict[str, MetricRule] = {}
        
        # 历史数据缓存
        self.metric_history: Dict[str, List[Dict]] = {}
        
        # 异常事件缓存
        self.recent_anomalies: List[AnomalyEvent] = []
        
        # 事件回调函数
        self.event_callbacks: List[Callable[[AnomalyEvent], None]] = []
        
        # 加载配置
        self._load_config(config_file)
    
    def _load_config(self, config_file: str):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 加载监控规则
            for rule_config in config.get("rules", []):
                rule = MetricRule(
                    metric_name=rule_config["metric_name"],
                    rule_type=AnomalyType(rule_config["rule_type"]),
                    threshold_high=rule_config.get("threshold_high"),
                    threshold_low=rule_config.get("threshold_low"),
                    window_size=rule_config.get("window_size", 10),
                    sensitivity=rule_config.get("sensitivity", 0.8),
                    enabled=rule_config.get("enabled", True),
                    context=rule_config.get("context", {})
                )
                self.rules[rule.metric_name] = rule
                
                self.logger.info(f"加载监控规则: {rule.metric_name}")
                
        except FileNotFoundError:
            self.logger.warning(f"配置文件 {config_file} 不存在，使用默认配置")
            self._create_default_config(config_file)
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            self._create_default_config(config_file)
    
    def _create_default_config(self, config_file: str):
        """创建默认配置"""
        default_config = {
            "rules": [
                {
                    "metric_name": "cpu_usage",
                    "rule_type": "threshold",
                    "threshold_high": 80.0,
                    "threshold_low": 5.0,
                    "window_size": 10,
                    "sensitivity": 0.8,
                    "enabled": True,
                    "context": {"unit": "percent", "source": "system"}
                },
                {
                    "metric_name": "memory_usage",
                    "rule_type": "threshold",
                    "threshold_high": 85.0,
                    "threshold_low": 10.0,
                    "window_size": 10,
                    "sensitivity": 0.8,
                    "enabled": True,
                    "context": {"unit": "percent", "source": "system"}
                },
                {
                    "metric_name": "response_time",
                    "rule_type": "threshold",
                    "threshold_high": 2000.0,
                    "window_size": 10,
                    "sensitivity": 0.8,
                    "enabled": True,
                    "context": {"unit": "ms", "source": "application"}
                },
                {
                    "metric_name": "error_rate",
                    "rule_type": "threshold", 
                    "threshold_high": 5.0,
                    "window_size": 10,
                    "sensitivity": 0.8,
                    "enabled": True,
                    "context": {"unit": "percent", "source": "application"}
                }
            ]
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            self.logger.info(f"已创建默认配置文件: {config_file}")
            self._load_config(config_file)
        except Exception as e:
            self.logger.error(f"创建默认配置失败: {e}")
    
    def add_event_callback(self, callback: Callable[[AnomalyEvent], None]):
        """添加事件回调函数"""
        self.event_callbacks.append(callback)
    
    def remove_event_callback(self, callback: Callable[[AnomalyEvent], None]):
        """移除事件回调函数"""
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)
    
    def _trigger_event_callbacks(self, event: AnomalyEvent):
        """触发事件回调"""
        for callback in self.event_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"事件回调执行失败: {e}")
    
    def _update_metric_history(self, metric_name: str, value: float, timestamp: str):
        """更新指标历史数据"""
        if metric_name not in self.metric_history:
            self.metric_history[metric_name] = []
        
        # 添加新数据点
        self.metric_history[metric_name].append({
            "value": value,
            "timestamp": timestamp
        })
        
        # 保持历史数据在合理范围内
        if len(self.metric_history[metric_name]) > 1000:
            self.metric_history[metric_name] = self.metric_history[metric_name][-1000:]
    
    def _calculate_deviation_score(self, current_value: float, history: List[float]) -> float:
        """计算偏差分数"""
        if len(history) < 2:
            return 0.0
        
        try:
            mean = statistics.mean(history)
            stdev = statistics.stdev(history) if len(history) > 1 else 0
            
            if stdev == 0:
                return 0.0
            
            # 计算Z分数
            z_score = abs(current_value - mean) / stdev
            
            # 转换为0-1范围的偏差分数
            deviation_score = min(z_score / 3.0, 1.0)
            
            return deviation_score
            
        except Exception as e:
            self.logger.error(f"计算偏差分数失败: {e}")
            return 0.0
    
    def _determine_severity(self, deviation_score: float, rule: MetricRule) -> Severity:
        """确定异常严重程度"""
        if deviation_score >= 0.9:
            return Severity.CRITICAL
        elif deviation_score >= 0.7:
            return Severity.HIGH
        elif deviation_score >= 0.5:
            return Severity.MEDIUM
        else:
            return Severity.LOW
    
    def _check_threshold_anomaly(self, metric_name: str, value: float, rule: MetricRule) -> Optional[AnomalyEvent]:
        """检查阈值异常"""
        anomaly_detected = False
        threshold = None
        description = ""
        
        # 检查高阈值
        if rule.threshold_high is not None and value > rule.threshold_high:
            anomaly_detected = True
            threshold = rule.threshold_high
            description = f"{metric_name} 超过高阈值 {rule.threshold_high}，当前值: {value}"
        
        # 检查低阈值
        elif rule.threshold_low is not None and value < rule.threshold_low:
            anomaly_detected = True
            threshold = rule.threshold_low
            description = f"{metric_name} 低于低阈值 {rule.threshold_low}，当前值: {value}"
        
        if not anomaly_detected:
            return None
        
        # 计算偏差分数
        history = [item["value"] for item in self.metric_history.get(metric_name, [])]
        deviation_score = self._calculate_deviation_score(value, history)
        
        # 创建异常事件
        event = AnomalyEvent(
            event_id=f"anomaly_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
            metric_name=metric_name,
            anomaly_type=AnomalyType.THRESHOLD,
            severity=self._determine_severity(deviation_score, rule),
            current_value=value,
            expected_value=None,
            threshold=threshold,
            deviation_score=deviation_score,
            timestamp=datetime.now().isoformat(),
            source="threshold_detector",
            context=rule.context or {},
            description=description
        )
        
        return event
    
    def _check_trend_anomaly(self, metric_name: str, value: float, rule: MetricRule) -> Optional[AnomalyEvent]:
        """检查趋势异常"""
        history = [item["value"] for item in self.metric_history.get(metric_name, [])]
        
        if len(history) < rule.window_size:
            return None
        
        # 计算最近窗口的趋势
        recent_values = history[-rule.window_size:]
        
        # 简单趋势检测：线性回归斜率
        try:
            n = len(recent_values)
            x_sum = sum(range(n))
            y_sum = sum(recent_values)
            xy_sum = sum(i * y for i, y in enumerate(recent_values))
            x2_sum = sum(i * i for i in range(n))
            
            # 计算斜率
            denominator = n * x2_sum - x_sum * x_sum
            if denominator == 0:
                return None
                
            slope = (n * xy_sum - x_sum * y_sum) / denominator
            
            # 计算趋势强度
            mean_value = statistics.mean(recent_values)
            trend_strength = abs(slope) / (mean_value + 1e-8)
            
            # 如果趋势强度超过敏感度阈值，则认为是异常
            if trend_strength > rule.sensitivity:
                trend_direction = "上升" if slope > 0 else "下降"
                
                event = AnomalyEvent(
                    event_id=f"anomaly_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                    metric_name=metric_name,
                    anomaly_type=AnomalyType.TREND,
                    severity=self._determine_severity(trend_strength, rule),
                    current_value=value,
                    expected_value=mean_value,
                    threshold=None,
                    deviation_score=trend_strength,
                    timestamp=datetime.now().isoformat(),
                    source="trend_detector",
                    context=rule.context or {},
                    description=f"{metric_name} 出现异常{trend_direction}趋势，趋势强度: {trend_strength:.3f}"
                )
                
                return event
        
        except Exception as e:
            self.logger.error(f"趋势检测失败: {e}")
        
        return None
    
    def detect_anomaly(self, metric_name: str, value: float, source: str = "unknown") -> Optional[AnomalyEvent]:
        """
        检测单个指标异常
        
        Args:
            metric_name: 指标名称
            value: 指标值
            source: 数据源
            
        Returns:
            检测到的异常事件，如果没有异常则返回None
        """
        timestamp = datetime.now().isoformat()
        
        # 更新历史数据
        self._update_metric_history(metric_name, value, timestamp)
        
        # 检查是否有对应的监控规则
        if metric_name not in self.rules:
            return None
        
        rule = self.rules[metric_name]
        
        # 检查规则是否启用
        if not rule.enabled:
            return None
        
        # 根据规则类型执行检测
        event = None
        
        if rule.rule_type == AnomalyType.THRESHOLD:
            event = self._check_threshold_anomaly(metric_name, value, rule)
        elif rule.rule_type == AnomalyType.TREND:
            event = self._check_trend_anomaly(metric_name, value, rule)
        
        # 如果检测到异常
        if event:
            # 更新事件源信息
            event.source = source
            
            # 添加到最近异常列表
            self.recent_anomalies.append(event)
            
            # 保持最近异常列表在合理大小
            if len(self.recent_anomalies) > 100:
                self.recent_anomalies = self.recent_anomalies[-100:]
            
            # 触发事件回调
            self._trigger_event_callbacks(event)
            
            self.logger.warning(f"检测到异常: {event.description}")
        
        return event
    
    def batch_detect(self, metrics: Dict[str, float], source: str = "batch") -> List[AnomalyEvent]:
        """
        批量检测多个指标异常
        
        Args:
            metrics: 指标字典 {metric_name: value}
            source: 数据源
            
        Returns:
            检测到的异常事件列表
        """
        anomalies = []
        
        for metric_name, value in metrics.items():
            event = self.detect_anomaly(metric_name, value, source)
            if event:
                anomalies.append(event)
        
        return anomalies
    
    def get_recent_anomalies(self, hours: int = 24) -> List[AnomalyEvent]:
        """获取最近的异常事件"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent = []
        for event in self.recent_anomalies:
            event_time = datetime.fromisoformat(event.timestamp)
            if event_time >= cutoff_time:
                recent.append(event)
        
        return recent
    
    def get_metric_statistics(self, metric_name: str) -> Dict[str, Any]:
        """获取指标统计信息"""
        if metric_name not in self.metric_history:
            return {}
        
        values = [item["value"] for item in self.metric_history[metric_name]]
        
        if not values:
            return {}
        
        stats = {
            "count": len(values),
            "mean": statistics.mean(values),
            "min": min(values),
            "max": max(values),
            "latest": values[-1],
            "latest_timestamp": self.metric_history[metric_name][-1]["timestamp"]
        }
        
        if len(values) > 1:
            stats["std"] = statistics.stdev(values)
        else:
            stats["std"] = 0.0
        
        return stats
    
    def update_rule(self, metric_name: str, rule: MetricRule):
        """更新监控规则"""
        self.rules[metric_name] = rule
        self.logger.info(f"更新监控规则: {metric_name}")
    
    def enable_rule(self, metric_name: str):
        """启用监控规则"""
        if metric_name in self.rules:
            self.rules[metric_name].enabled = True
            self.logger.info(f"启用监控规则: {metric_name}")
    
    def disable_rule(self, metric_name: str):
        """禁用监控规则"""
        if metric_name in self.rules:
            self.rules[metric_name].enabled = False
            self.logger.info(f"禁用监控规则: {metric_name}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取检测器状态"""
        return {
            "rules_count": len(self.rules),
            "enabled_rules": len([r for r in self.rules.values() if r.enabled]),
            "total_metrics": len(self.metric_history),
            "recent_anomalies": len(self.get_recent_anomalies(24)),
            "callbacks_count": len(self.event_callbacks)
        }


def test_anomaly_detector():
    """测试异常检测器"""
    import random
    
    # 创建检测器
    detector = AnomalyDetector()
    
    # 添加事件回调
    def on_anomaly(event: AnomalyEvent):
        print(f"🚨 异常检测: {event.description}")
        print(f"   严重程度: {event.severity.value}")
        print(f"   偏差分数: {event.deviation_score:.3f}")
    
    detector.add_event_callback(on_anomaly)
    
    # 模拟正常数据
    print("=== 模拟正常数据 ===")
    for i in range(10):
        metrics = {
            "cpu_usage": 50 + random.gauss(0, 5),
            "memory_usage": 60 + random.gauss(0, 3),
            "response_time": 500 + random.gauss(0, 50)
        }
        
        anomalies = detector.batch_detect(metrics, "simulation")
        if not anomalies:
            print(f"时间点 {i}: 正常")
    
    # 模拟异常数据
    print("\n=== 模拟异常数据 ===")
    abnormal_metrics = {
        "cpu_usage": 95.0,  # 超过阈值
        "memory_usage": 90.0,  # 超过阈值
        "response_time": 3000.0  # 超过阈值
    }
    
    anomalies = detector.batch_detect(abnormal_metrics, "simulation")
    
    # 显示统计信息
    print(f"\n=== 统计信息 ===")
    for metric in ["cpu_usage", "memory_usage", "response_time"]:
        stats = detector.get_metric_statistics(metric)
        print(f"{metric}: 均值={stats.get('mean', 0):.2f}, 标准差={stats.get('std', 0):.2f}")
    
    # 显示检测器状态
    status = detector.get_status()
    print(f"\n检测器状态: {json.dumps(status, ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    test_anomaly_detector() 