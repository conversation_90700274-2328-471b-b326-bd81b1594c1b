#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR校验异常处理任务监控器 - 每小时检查任务状态
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from pathlib import Path
import httpx


class TaskMonitor:
    """任务状态监控器"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000", config_file: str = "task_monitor_config.json"):
        """
        初始化任务监控器
        
        Args:
            api_base_url: OCR校验系统API地址
            config_file: 监控配置文件
        """
        self.api_base_url = api_base_url
        self.config_file = config_file
        self.config = self._load_config()
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('task_monitor.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.client = httpx.AsyncClient(timeout=30.0)
    
    def _load_config(self) -> Dict:
        """加载监控配置"""
        default_config = {
            "check_interval_minutes": 60,  # 检查间隔（分钟）
            "max_retries": 3,              # 最大重试次数
            "enable_notifications": True,   # 是否启用通知
            "log_level": "INFO"
        }
        
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return {**default_config, **config}
            else:
                # 创建默认配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                self.logger.info(f"创建默认配置文件: {self.config_file}")
                return default_config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return default_config
    
    async def check_system_health(self) -> bool:
        """检查系统健康状态"""
        try:
            response = await self.client.get(f"{self.api_base_url}/health")
            if response.status_code == 200:
                result = response.json()
                return result.get("status") == "healthy"
            return False
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            return False
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取所有活跃任务"""
        try:
            response = await self.client.get(f"{self.api_base_url}/api/active_tasks")
            if response.status_code == 200:
                tasks_data = response.json()
                return [
                    {"task_id": task_id, **task_info}
                    for task_id, task_info in tasks_data.items()
                    if task_info.get("status") == "active"
                ]
            return []
        except Exception as e:
            self.logger.error(f"获取活跃任务失败: {e}")
            return []
    
    async def check_task_status(self, task_id: str) -> Dict[str, Any]:
        """检查单个任务状态"""
        try:
            response = await self.client.get(f"{self.api_base_url}/api/task_status/{task_id}")
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
        except Exception as e:
            self.logger.error(f"检查任务状态失败 {task_id}: {e}")
            return {"error": str(e)}
    
    async def process_task_updates(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理任务状态更新"""
        results = {
            "total_tasks": len(tasks),
            "updated_tasks": 0,
            "resolved_tasks": 0,
            "error_tasks": 0,
            "monitoring_tasks": 0,
            "completed_tasks": 0,
            "task_details": []
        }
        
        for task in tasks:
            task_id = task["task_id"]
            self.logger.info(f"检查任务状态: {task_id}")
            
            status_result = await self.check_task_status(task_id)
            
            task_detail = {
                "task_id": task_id,
                "alert_type": task.get("alert_type"),
                "start_time": task.get("start_time"),
                "status_check_result": status_result
            }
            
            current_status = status_result.get("status")
            
            if current_status == "resolved":
                results["resolved_tasks"] += 1
                self.logger.info(f"任务已解决: {task_id}")
            elif current_status == "monitoring":
                results["monitoring_tasks"] += 1
                self.logger.info(f"任务监控中: {task_id}")
            elif current_status == "completed":
                results["completed_tasks"] += 1
                self.logger.info(f"任务监控完成: {task_id}")
            elif current_status == "error":
                results["error_tasks"] += 1
                self.logger.error(f"任务检查出错: {task_id}")
            
            results["updated_tasks"] += 1
            results["task_details"].append(task_detail)
        
        return results
    
    async def send_monitoring_report(self, results: Dict[str, Any]):
        """发送监控报告（如果启用通知）"""
        if not self.config.get("enable_notifications", True):
            return
        
        # 构建报告内容
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report_content = f"""📊 **OCR校验系统监控报告**

⏰ **监控时间**: {report_time}

📈 **任务统计**:
• 总任务数: {results['total_tasks']}
• 已解决: {results['resolved_tasks']}
• 监控中: {results['monitoring_tasks']} 
• 已完成: {results['completed_tasks']}
• 出错: {results['error_tasks']}

💡 **监控详情**:
"""
        
        # 添加任务详情
        for task_detail in results["task_details"][:5]:  # 只显示前5个任务
            task_id = task_detail["task_id"]
            alert_type = task_detail["alert_type"]
            status = task_detail["status_check_result"].get("status", "unknown")
            
            report_content += f"• {task_id} ({alert_type}): {status}\n"
        
        if len(results["task_details"]) > 5:
            report_content += f"... 还有{len(results['task_details']) - 5}个任务\n"
        
        report_content += f"\n🔄 下次检查时间: {(datetime.now() + timedelta(minutes=self.config['check_interval_minutes'])).strftime('%H:%M')}"
        
        self.logger.info(f"监控报告:\n{report_content}")
        
        # 这里可以添加发送企业微信通知的逻辑
        # 暂时只记录到日志
    
    async def run_monitoring_cycle(self):
        """运行一次监控周期"""
        cycle_start_time = datetime.now()
        self.logger.info(f"开始监控周期: {cycle_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 检查系统健康状态
            if not await self.check_system_health():
                self.logger.warning("OCR校验系统不健康，跳过此次检查")
                return
            
            # 2. 获取活跃任务
            active_tasks = await self.get_active_tasks()
            self.logger.info(f"发现 {len(active_tasks)} 个活跃任务")
            
            if not active_tasks:
                self.logger.info("没有活跃任务需要检查")
                return
            
            # 3. 处理任务状态更新
            results = await self.process_task_updates(active_tasks)
            
            # 4. 发送监控报告
            await self.send_monitoring_report(results)
            
            cycle_end_time = datetime.now()
            cycle_duration = (cycle_end_time - cycle_start_time).total_seconds()
            
            self.logger.info(f"监控周期完成，耗时: {cycle_duration:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"监控周期执行失败: {e}")
    
    async def start_monitoring(self):
        """启动持续监控"""
        self.logger.info("启动OCR校验系统任务监控器")
        self.logger.info(f"检查间隔: {self.config['check_interval_minutes']} 分钟")
        
        try:
            while True:
                await self.run_monitoring_cycle()
                
                # 等待下次检查
                await asyncio.sleep(self.config['check_interval_minutes'] * 60)
                
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，任务监控器退出")
        except Exception as e:
            self.logger.error(f"监控器运行失败: {e}")
        finally:
            await self.client.aclose()
    
    async def run_once(self):
        """运行一次监控（用于测试）"""
        try:
            await self.run_monitoring_cycle()
        finally:
            await self.client.aclose()


async def main():
    """主函数"""
    import sys
    
    monitor = TaskMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "once":
        # 运行一次监控
        print("🔄 运行一次监控检查...")
        await monitor.run_once()
        print("✅ 监控检查完成")
    else:
        # 启动持续监控
        print("🚀 启动OCR校验系统任务监控器")
        print("   监控间隔: 每小时一次")
        print("   停止监控: Ctrl+C")
        print("-" * 50)
        await monitor.start_monitoring()


if __name__ == "__main__":
    asyncio.run(main()) 