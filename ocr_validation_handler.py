#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR校验异常处理系统 - 自动化处理OCR校验不通过的情况
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from mcp_universal_client import MCPMultiServerClient


class OCRValidationAlert(BaseModel):
    """OCR校验异常告警模型"""
    alert_type: str = Field(..., description="异常类型: completeness(完备性) 或 accuracy(准确性)")
    documents: List[str] = Field(..., description="借据编号列表")
    details: str = Field(..., description="异常详情描述")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="时间戳")
    severity: str = Field(default="medium", description="严重程度: low, medium, high, critical")


class OCRValidationConfig(BaseModel):
    """OCR校验处理配置"""
    workspace_id: int = Field(default=55191012, description="TAPD工作空间ID")
    
    # 责任人配置
    responsible_persons: Dict[str, str] = Field(
        default={
            "completeness": "产品经理",  # 完备性问题责任人
            "accuracy": "开发工程师"     # 准确性问题责任人
        },
        description="异常类型对应的责任人"
    )
    
    # 企业微信配置
    wechat_webhook_url: str = Field(
        default="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bd0495a6-72f2-4bbf-9dd0-39d4cb28f31d",
        description="企业微信Webhook URL"
    )
    
    # 处理时效配置
    processing_deadline_hours: int = Field(default=48, description="处理时效（小时）")
    check_interval_hours: int = Field(default=1, description="状态检查间隔（小时）")
    check_duration_hours: int = Field(default=24, description="检查持续时间（小时）")


class OCRValidationHandler:
    """OCR校验异常处理器"""
    
    def __init__(self, config_file: str = 'ocr_validation_config.json', mcp_config_file: str = 'mcp_config.json'):
        """
        初始化OCR校验异常处理器
        
        Args:
            config_file: 配置文件路径
            mcp_config_file: MCP配置文件路径
        """
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config_file)
        self.mcp_client = MCPMultiServerClient(mcp_config_file)
        
        # 任务跟踪
        self.active_tasks = {}  # 存储活跃的处理任务
        
        self.logger.info("✅ OCR校验异常处理器初始化完成")
    
    def _load_config(self, config_file: str) -> OCRValidationConfig:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                return OCRValidationConfig(**config_data)
        except FileNotFoundError:
            self.logger.warning(f"配置文件 {config_file} 未找到，使用默认配置")
            return OCRValidationConfig()
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件格式错误: {e}")
            return OCRValidationConfig()
    
    async def handle_ocr_validation_alert(self, alert: OCRValidationAlert) -> Dict[str, Any]:
        """
        处理OCR校验异常告警
        
        Args:
            alert: 异常告警数据
            
        Returns:
            处理结果
        """
        self.logger.info(f"收到OCR校验异常告警: {alert.alert_type}")
        
        tapd_result = {}
        wechat_result = {}
        task_id = None
        
        try:
            # 1. 创建TAPD需求
            try:
                tapd_result = await self._create_tapd_requirements(alert)
                self.logger.info("TAPD需求创建成功")
            except Exception as e:
                self.logger.error(f"创建TAPD需求失败: {e}")
                tapd_result = {"error": str(e)}
            
            # 2. 发送企业微信通知（即使TAPD失败也要尝试发送通知）
            try:
                wechat_result = await self._send_wechat_notification(alert, tapd_result)
                self.logger.info("企业微信通知发送成功")
            except Exception as e:
                self.logger.error(f"发送企业微信通知失败: {e}")
                wechat_result = {"error": str(e)}
            
            # 3. 启动状态监控任务（只有TAPD成功时才启动）
            try:
                if not tapd_result.get("error"):
                    task_id = self._start_monitoring_task(alert, tapd_result)
                    self.logger.info(f"状态监控任务启动成功: {task_id}")
            except Exception as e:
                self.logger.error(f"启动状态监控任务失败: {e}")
            
            result = {
                "success": not tapd_result.get("error") and not wechat_result.get("error"),
                "alert_id": f"OCR_{alert.alert_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "tapd_result": tapd_result,
                "wechat_result": wechat_result,
                "monitoring_task_id": task_id,
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"OCR校验异常处理完成: {result['alert_id']}")
            return result
            
        except Exception as e:
            self.logger.error(f"OCR校验异常处理失败: {e}")
            return {
                "success": False,
                "alert_id": f"OCR_{alert.alert_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "error": str(e),
                "tapd_result": tapd_result,
                "wechat_result": wechat_result,
                "monitoring_task_id": task_id,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _create_tapd_requirements(self, alert: OCRValidationAlert) -> Dict[str, Any]:
        """创建TAPD需求"""
        try:
            current_date = datetime.now().strftime('%Y年%m月%d日')
            
            # 根据异常类型确定标题
            alert_type_map = {
                "completeness": "完备性",
                "accuracy": "准确性"
            }
            alert_type_chinese = alert_type_map.get(alert.alert_type, alert.alert_type)
            
            # 创建父需求
            parent_story_name = f"OCR{alert_type_chinese}校验不通过-{current_date}"
            parent_story_description = f"""
## 异常概述
- **异常类型**: OCR{alert_type_chinese}校验不通过
- **发现时间**: {alert.timestamp}
- **严重程度**: {alert.severity}

## 基本信息
- **涉及借据数量**: {len(alert.documents)}
- **处理时效**: {self.config.processing_deadline_hours}小时内完成
- **责任人**: {self.config.responsible_persons.get(alert.alert_type, '待指定')}

## 处理要求
1. 请在{self.config.processing_deadline_hours}小时内处理完成
2. 处理完成后请提交评论说明故障原因
3. 可选择的故障原因：
   - 数据源问题
   - OCR引擎问题  
   - 配置参数问题
   - 系统环境问题
   - 其他（请详细说明）
            """
            
            parent_story_params = {
                "workspace_id": self.config.workspace_id,
                "name": parent_story_name,
                "description": parent_story_description,
                "priority": "高" if alert.severity in ["high", "critical"] else "中",
                "status": "新建"
            }
            
            self.logger.info(f"创建TAPD父需求: {parent_story_name}")
            parent_result = await self.mcp_client.find_and_call_tool("create_story_or_task", parent_story_params)

            # 检查是否是错误响应
            if parent_result.get("error") or parent_result.get("fallback_mode"):
                self.logger.error(f"创建父需求失败: {parent_result.get('error', 'Unknown error')}")
                # 如果TAPD失败，创建一个模拟响应以便系统继续运行
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                parent_result = {
                    "error": f"TAPD API调用失败: {parent_result.get('error', 'Unknown error')}",
                    "fallback_mode": True,
                    "story_name": parent_story_name,
                    "created_time": current_time,
                    "workspace_url": f"https://www.tapd.cn/{self.config.workspace_id}"
                }
            else:
                self.logger.info("父需求创建成功")
            
            # 创建子需求
            child_story_name = f"OCR{alert_type_chinese}校验异常处理-详细信息"
            child_story_description = f"""
## 详细异常信息
- **异常类型**: {alert_type_chinese}校验不通过
- **异常详情**: {alert.details}

## 涉及借据列表
{chr(10).join([f"- {doc}" for doc in alert.documents])}

## 数据片段信息
- **借据总数**: {len(alert.documents)}
- **异常发现时间**: {alert.timestamp}
- **数据来源**: OCR校验系统

## 排查建议
1. 检查OCR识别准确率
2. 验证数据完整性
3. 确认系统配置正确性
4. 分析异常数据特征

## 处理流程
1. 分析异常原因
2. 制定解决方案
3. 实施修复措施
4. 验证修复效果
5. 提交处理报告
            """
            
            child_story_params = {
                "workspace_id": self.config.workspace_id,
                "name": child_story_name,
                "description": child_story_description,
                "priority": "中",
                "status": "新建"
            }
            
            self.logger.info(f"创建TAPD子需求: {child_story_name}")
            child_result = await self.mcp_client.find_and_call_tool("create_story_or_task", child_story_params)

            # 检查是否是错误响应
            if child_result.get("error") or child_result.get("fallback_mode"):
                self.logger.error(f"创建子需求失败: {child_result.get('error', 'Unknown error')}")
                # 如果子需求创建失败，创建模拟响应
                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                child_result = {
                    "error": f"TAPD API调用失败: {child_result.get('error', 'Unknown error')}",
                    "fallback_mode": True,
                    "story_name": child_story_name,
                    "created_time": current_time,
                    "workspace_url": f"https://www.tapd.cn/{self.config.workspace_id}"
                }
            else:
                self.logger.info("子需求创建成功")
            
            return {
                "parent_story": parent_result,
                "child_story": child_result,
                "parent_story_name": parent_story_name,
                "child_story_name": child_story_name
            }
            
        except Exception as e:
            self.logger.error(f"创建TAPD需求失败: {e}")
            raise
    
    async def _send_wechat_notification(self, alert: OCRValidationAlert, tapd_result: Dict[str, Any]) -> Dict[str, Any]:
        """发送企业微信通知"""
        try:
            # 根据异常类型确定中文描述
            alert_type_map = {
                "completeness": "完备性",
                "accuracy": "准确性"
            }
            alert_type_chinese = alert_type_map.get(alert.alert_type, alert.alert_type)
            
            # 获取责任人
            responsible_person = self.config.responsible_persons.get(alert.alert_type, '待指定')
            
            # 构建通知消息
            message_content = f"""🚨 OCR校验异常警告

异常摘要: OCR{alert_type_chinese}校验不通过
关键数据片段: {len(alert.documents)}个借据受影响
处理时效: {self.config.processing_deadline_hours}小时内处理完成

涉及借据编号:
{chr(10).join([f"• {doc}" for doc in alert.documents[:10]])}
{f"... 还有{len(alert.documents) - 10}个借据" if len(alert.documents) > 10 else ""}

异常详情: {alert.details}

责任人: @{responsible_person}

TAPD需求链接: 
- 父需求: {tapd_result.get('parent_story_name', 'N/A')}
- 子需求: {tapd_result.get('child_story_name', 'N/A')}

处理要求:
1. 请在{self.config.processing_deadline_hours}小时内处理完成
2. 处理完成后请在TAPD中提交评论说明故障原因

发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            # 使用TAPD的MCP服务发送企业微信通知
            wechat_params = {
                "message": message_content,
                "webhook_url": self.config.wechat_webhook_url
            }
            
            self.logger.info(f"发送企业微信通知给责任人: {responsible_person}")
            # 首先尝试通过TAPD MCP发送
            result = await self.mcp_client.find_and_call_tool("send_qiwei_message", wechat_params)

            # 检查是否是错误响应
            if result.get("error") or result.get("fallback_mode"):
                self.logger.error(f"通过TAPD MCP发送企业微信消息失败: {result.get('error', 'Unknown error')}")
                # 如果TAPD MCP失败，尝试直接发送HTTP请求
                try:
                    result = await self._send_wechat_direct(message_content)
                    self.logger.info("通过直接HTTP发送企业微信消息成功")
                except Exception as direct_error:
                    self.logger.error(f"直接发送企业微信消息也失败: {direct_error}")
                    result = {
                        "error": f"所有发送方式都失败: TAPD MCP: {result.get('error', 'Unknown error')}, 直接HTTP: {str(direct_error)}",
                        "fallback_mode": True,
                        "message_content": message_content
                    }
            else:
                self.logger.info("企业微信消息发送成功")
            
            return {
                "success": True,
                "message_content": message_content,
                "responsible_person": responsible_person,
                "send_result": result
            }
            
        except Exception as e:
            self.logger.error(f"发送企业微信通知失败: {e}")
            raise
    
    async def _send_wechat_direct(self, message_content: str) -> Dict[str, Any]:
        """直接通过HTTP发送企业微信消息"""
        try:
            try:
                import httpx
            except ImportError:
                # 如果httpx不可用，尝试使用requests
                import requests
                
                message = {
                    "msgtype": "text",
                    "text": {
                        "content": message_content
                    }
                }
                
                response = requests.post(
                    self.config.wechat_webhook_url,
                    json=message,
                    headers={"Content-Type": "application/json"},
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return {
                            "success": True,
                            "send_method": "direct_requests",
                            "response": result
                        }
                    else:
                        raise Exception(f"企业微信API返回错误: {result.get('errmsg')}")
                else:
                    raise Exception(f"HTTP请求失败，状态码: {response.status_code}")
                    
                return  # 避免继续执行下面的httpx代码
            
            # 构建企业微信消息体
            message = {
                "msgtype": "text",
                "text": {
                    "content": message_content
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.config.wechat_webhook_url,
                    json=message,
                    headers={"Content-Type": "application/json"},
                    timeout=10.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return {
                            "success": True,
                            "send_method": "direct_http",
                            "response": result
                        }
                    else:
                        raise Exception(f"企业微信API返回错误: {result.get('errmsg')}")
                else:
                    raise Exception(f"HTTP请求失败，状态码: {response.status_code}")
                    
        except Exception as e:
            self.logger.error(f"直接发送企业微信消息失败: {e}")
            raise
    
    def _start_monitoring_task(self, alert: OCRValidationAlert, tapd_result: Dict[str, Any]) -> str:
        """启动状态监控任务"""
        task_id = f"monitor_{alert.alert_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 存储任务信息
        self.active_tasks[task_id] = {
            "alert": alert,
            "tapd_result": tapd_result,
            "start_time": datetime.now(),
            "next_check_time": datetime.now() + timedelta(hours=self.config.check_interval_hours),
            "end_time": datetime.now() + timedelta(hours=self.config.check_duration_hours),
            "status": "active"
        }
        
        self.logger.info(f"启动状态监控任务: {task_id}")
        return task_id
    
    async def check_task_status(self, task_id: str) -> Dict[str, Any]:
        """检查任务状态（24小时内每小时检查一次）"""
        if task_id not in self.active_tasks:
            raise ValueError(f"任务 {task_id} 不存在")
        
        task = self.active_tasks[task_id]
        current_time = datetime.now()
        
        try:
            # 检查是否到了检查时间
            if current_time < task["next_check_time"]:
                return {
                    "task_id": task_id,
                    "status": "waiting",
                    "next_check_time": task["next_check_time"].isoformat(),
                    "message": "还未到检查时间"
                }
            
            # 检查是否超过监控期限
            if current_time > task["end_time"]:
                task["status"] = "completed"
                return {
                    "task_id": task_id,
                    "status": "completed",
                    "message": "监控期限已结束"
                }
            
            # 获取TAPD需求状态
            parent_story_id = self._extract_story_id(task["tapd_result"]["parent_story"])
            child_story_id = self._extract_story_id(task["tapd_result"]["child_story"])
            
            if parent_story_id:
                story_params = {
                    "workspace_id": self.config.workspace_id,
                    "id": parent_story_id
                }
                story_status = await self.mcp_client.find_and_call_tool("get_stories_or_tasks", story_params)
                
                # 检查需求状态
                if self._is_story_completed(story_status):
                    task["status"] = "resolved"
                    return {
                        "task_id": task_id,
                        "status": "resolved",
                        "message": "需求已完成处理",
                        "story_status": story_status
                    }
            
            # 更新下次检查时间
            task["next_check_time"] = current_time + timedelta(hours=self.config.check_interval_hours)
            
            return {
                "task_id": task_id,
                "status": "monitoring",
                "next_check_time": task["next_check_time"].isoformat(),
                "message": "继续监控中"
            }
            
        except Exception as e:
            self.logger.error(f"检查任务状态失败: {e}")
            return {
                "task_id": task_id,
                "status": "error",
                "error": str(e)
            }
    
    def _extract_story_id(self, story_result: Dict) -> Optional[str]:
        """从TAPD创建结果中提取需求ID"""
        try:
            if isinstance(story_result, dict) and "data" in story_result:
                data = story_result["data"]
                if isinstance(data, dict) and "Story" in data:
                    return data["Story"].get("id")
                elif isinstance(data, list) and len(data) > 0:
                    return data[0].get("Story", {}).get("id")
            return None
        except Exception:
            return None
    
    def _is_story_completed(self, story_status: Dict) -> bool:
        """判断需求是否已完成"""
        try:
            if isinstance(story_status, dict) and "data" in story_status:
                data = story_status["data"]
                if isinstance(data, list) and len(data) > 0:
                    story = data[0].get("Story", {})
                    status = story.get("status", "")
                    return status in ["已完成", "已关闭", "已验收"]
            return False
        except Exception:
            return False
    
    def get_active_tasks(self) -> Dict[str, Any]:
        """获取所有活跃任务"""
        return {
            task_id: {
                "status": task["status"],
                "start_time": task["start_time"].isoformat(),
                "next_check_time": task["next_check_time"].isoformat() if task["status"] == "active" else None,
                "end_time": task["end_time"].isoformat(),
                "alert_type": task["alert"]["alert_type"]
            }
            for task_id, task in self.active_tasks.items()
        }


# FastAPI应用
app = FastAPI(title="OCR校验异常处理系统", version="1.0.0")

# 全局处理器实例
handler: Optional[OCRValidationHandler] = None


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global handler
    handler = OCRValidationHandler()


@app.post("/api/ocr_validation_alert")
async def receive_ocr_validation_alert(
    alert: OCRValidationAlert,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    """
    接收OCR校验异常告警
    
    Args:
        alert: OCR校验异常告警数据
        background_tasks: 后台任务
        
    Returns:
        处理结果
    """
    if handler is None:
        raise HTTPException(status_code=500, detail="处理器未初始化")
    
    try:
        # 异步处理告警
        result = await handler.handle_ocr_validation_alert(alert)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理异常告警失败: {str(e)}")


@app.get("/api/task_status/{task_id}")
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务状态信息
    """
    if handler is None:
        raise HTTPException(status_code=500, detail="处理器未初始化")
    
    try:
        result = await handler.check_task_status(task_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@app.get("/api/active_tasks")
async def get_active_tasks() -> Dict[str, Any]:
    """获取所有活跃任务"""
    if handler is None:
        raise HTTPException(status_code=500, detail="处理器未初始化")
    
    return handler.get_active_tasks()


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)