#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR校验异常处理系统启动脚本
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
import uvicorn
from ocr_validation_handler import app


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('ocr_validation_system.log', encoding='utf-8')
        ]
    )


def check_environment():
    """检查环境配置"""
    required_files = [
        'mcp_config.json',
        'ocr_validation_config.json'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要的配置文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n💡 请确保以下文件存在:")
        print("   - mcp_config.json: MCP服务器配置")
        print("   - ocr_validation_config.json: OCR校验处理配置")
        return False
    
    print("✅ 环境配置检查通过")
    return True


def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("🚀 OCR校验异常处理系统")
    print("=" * 60)
    print("📋 系统功能:")
    print("   1. 接收OCR校验异常告警")
    print("   2. 自动创建TAPD需求（父需求 + 子需求）")
    print("   3. 发送企业微信通知给责任人")
    print("   4. 24小时内每小时检查需求状态")
    print("")
    print("🔗 API端点:")
    print("   POST /api/ocr_validation_alert - 接收异常告警")
    print("   GET  /api/task_status/{task_id} - 查询任务状态")
    print("   GET  /api/active_tasks - 查询活跃任务")
    print("   GET  /health - 健康检查")
    print("")
    print("📊 支持的异常类型:")
    print("   - completeness: OCR完备性校验不通过")
    print("   - accuracy: OCR准确性校验不通过")
    print("=" * 60)


async def test_mcp_connection():
    """测试MCP连接"""
    try:
        from mcp_universal_client import MCPMultiServerClient
        
        print("🔄 测试MCP连接...")
        mcp_client = MCPMultiServerClient('mcp_config.json')
        
        # 测试获取工具列表
        tools = mcp_client.list_all_tools_sync()
        print(f"✅ MCP连接成功，发现 {len(tools)} 个服务器")
        
        # 显示可用工具
        for server_name, server_tools in tools.items():
            print(f"   📋 {server_name}: {len(server_tools)} 个工具")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP连接失败: {e}")
        print("💡 请检查:")
        print("   1. mcp_config.json 配置是否正确")
        print("   2. TAPD API 凭据是否有效")
        print("   3. 企业微信 Webhook URL 是否可用")
        return False


def main():
    """主函数"""
    print_startup_info()
    
    # 设置日志
    setup_logging()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 测试MCP连接
    if not asyncio.run(test_mcp_connection()):
        print("\n⚠️  MCP连接测试失败，但系统仍将启动")
        print("   如果遇到问题，请检查配置后重启")
    
    print("\n🌟 系统启动中...")
    print("   访问地址: http://localhost:8000")
    print("   API文档: http://localhost:8000/docs")
    print("   停止服务: Ctrl+C")
    print("\n" + "=" * 60)
    
    # 启动FastAPI服务器
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n\n👋 OCR校验异常处理系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 