#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用 MCP 客户端 - 动态发现和调用 MCP 工具
"""

import json
import asyncio
import logging
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class UniversalMCPClient:
    """通用 MCP 客户端 - 可以连接任何 MCP 服务器"""
    
    def __init__(self, mcp_config_file: str = 'mcp_config.json', server_name: Optional[str] = None):
        """
        初始化通用 MCP 客户端
        
        Args:
            mcp_config_file: MCP配置文件路径
            server_name: 指定要连接的服务器名称，如果为None则使用第一个服务器
        """
        self.mcp_config_file = mcp_config_file
        self.mcp_config = self._load_mcp_config()
        self.server_name = server_name or list(self.mcp_config.get('mcpServers', {}).keys())[0]
        self.server_config = self.mcp_config.get('mcpServers', {}).get(self.server_name, {})
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 验证配置
        if not self.server_config:
            raise ValueError(f"MCP服务器 '{self.server_name}' 配置未找到，请检查{self.mcp_config_file}文件")
        
        self.logger.info(f"✅ 连接到 MCP 服务器: {self.server_name}")
    
    def _load_mcp_config(self) -> Dict:
        """加载MCP配置文件"""
        try:
            with open(self.mcp_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"MCP配置文件 {self.mcp_config_file} 未找到")
        except json.JSONDecodeError as e:
            raise ValueError(f"MCP配置文件格式错误: {e}")
    
    def _get_env_vars(self) -> Dict[str, str]:
        """获取环境变量"""
        env_vars = self.server_config.get('env', {})
        # 将当前环境变量与MCP配置的环境变量合并
        current_env = os.environ.copy()
        current_env.update(env_vars)
        return current_env
    
    def _create_mcp_session(self):
        """创建MCP会话"""
        # 获取服务器参数
        command = self.server_config.get('command', 'uvx')
        args = self.server_config.get('args', [])
        env_vars = self._get_env_vars()
        
        # 创建服务器参数
        server_params = StdioServerParameters(
            command=command,
            args=args,
            env=env_vars
        )
        
        # 创建MCP客户端会话
        return stdio_client(server_params)
    
    async def list_tools(self) -> List[Dict]:
        """
        获取所有可用工具及其描述
        
        Returns:
            工具列表，每个工具包含名称、描述和参数信息
        """
        try:
            async with self._create_mcp_session() as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    tools_response = await session.list_tools()
                    
                    tools_info = []
                    for tool in tools_response.tools:
                        tool_info = {
                            'name': tool.name,
                            'description': tool.description or '无描述',
                            'parameters': {}
                        }
                        
                        # 解析工具参数
                        if hasattr(tool, 'inputSchema') and tool.inputSchema:
                            schema = tool.inputSchema
                            if isinstance(schema, dict):
                                properties = schema.get('properties', {})
                                required = schema.get('required', [])
                                
                                for param_name, param_info in properties.items():
                                    tool_info['parameters'][param_name] = {
                                        'type': param_info.get('type', 'unknown'),
                                        'description': param_info.get('description', '无描述'),
                                        'required': param_name in required
                                    }
                        
                        tools_info.append(tool_info)
                    
                    return tools_info
                    
        except Exception as e:
            self.logger.error(f"获取工具列表失败: {e}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """
        调用指定的 MCP 工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        session_context = None
        try:
            self.logger.info(f"调用工具: {tool_name}, 参数: {arguments}")
            
            session_context = self._create_mcp_session()
            async with session_context as (read, write):
                async with ClientSession(read, write) as session:
                    try:
                        await session.initialize()
                        
                        # 获取可用工具
                        tools = await session.list_tools()
                        
                        # 查找目标工具
                        target_tool = None
                        for tool in tools.tools:
                            if tool.name == tool_name:
                                target_tool = tool
                                break
                        
                        if not target_tool:
                            available_tools = [t.name for t in tools.tools]
                            raise Exception(f"工具 '{tool_name}' 不存在。可用工具: {available_tools}")
                        
                        # 调用工具
                        result = await session.call_tool(tool_name, arguments)
                        
                        if hasattr(result, 'isError') and result.isError:
                            error_msg = getattr(result, 'error', 'Unknown error')
                            raise Exception(f"工具调用失败: {error_msg}")
                        elif hasattr(result, 'content') and any(hasattr(c, 'text') and 'error' in c.text.lower() for c in result.content if hasattr(c, 'text')):
                            error_text = next((c.text for c in result.content if hasattr(c, 'text') and 'error' in c.text.lower()), 'Unknown error')
                            raise Exception(f"工具调用失败: {error_text}")
                        
                        # 处理结果
                        content = result.content
                        if content:
                            if len(content) == 1 and hasattr(content[0], 'text'):
                                try:
                                    return json.loads(content[0].text)
                                except json.JSONDecodeError:
                                    return {"result": content[0].text}
                            else:
                                return {"content": [str(c) for c in content]}
                        else:
                            return {"result": "操作成功"}
                    
                    except Exception as session_error:
                        self.logger.error(f"MCP会话错误: {session_error}")
                        raise
                        
        except Exception as e:
            self.logger.error(f"工具调用失败: {e}")
            raise
        finally:
            # 确保资源清理
            if session_context:
                try:
                    # 强制等待一小段时间让资源清理
                    await asyncio.sleep(0.1)
                except:
                    pass
    
    async def get_tool_info(self, tool_name: str) -> Optional[Dict]:
        """
        获取指定工具的详细信息
        
        Args:
            tool_name: 工具名称
            
        Returns:
            工具信息，如果工具不存在则返回None
        """
        tools = await self.list_tools()
        for tool in tools:
            if tool['name'] == tool_name:
                return tool
        return None
    
    # 同步包装方法
    def list_tools_sync(self) -> List[Dict]:
        """同步获取工具列表"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.list_tools())
                    return future.result()
            else:
                return asyncio.run(self.list_tools())
        except RuntimeError:
            return asyncio.run(self.list_tools())
    
    def call_tool_sync(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """同步调用工具"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.call_tool(tool_name, arguments))
                    return future.result()
            else:
                return asyncio.run(self.call_tool(tool_name, arguments))
        except RuntimeError:
            return asyncio.run(self.call_tool(tool_name, arguments))
    
    def get_tool_info_sync(self, tool_name: str) -> Optional[Dict]:
        """同步获取工具信息"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.get_tool_info(tool_name))
                    return future.result()
            else:
                return asyncio.run(self.get_tool_info(tool_name))
        except RuntimeError:
            return asyncio.run(self.get_tool_info(tool_name))


class MCPMultiServerClient:
    """多服务器 MCP 客户端 - 可以管理多个 MCP 服务器"""
    
    def __init__(self, mcp_config_file: str = 'mcp_config.json'):
        """
        初始化多服务器 MCP 客户端
        
        Args:
            mcp_config_file: MCP配置文件路径
        """
        self.mcp_config_file = mcp_config_file
        self.mcp_config = self._load_mcp_config()
        self.servers = {}
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 初始化所有服务器客户端
        for server_name in self.mcp_config.get('mcpServers', {}):
            try:
                self.servers[server_name] = UniversalMCPClient(mcp_config_file, server_name)
                self.logger.info(f"✅ 初始化服务器: {server_name}")
            except Exception as e:
                self.logger.error(f"❌ 初始化服务器 {server_name} 失败: {e}")
    
    def _load_mcp_config(self) -> Dict:
        """加载MCP配置文件"""
        try:
            with open(self.mcp_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"MCP配置文件 {self.mcp_config_file} 未找到")
        except json.JSONDecodeError as e:
            raise ValueError(f"MCP配置文件格式错误: {e}")
    
    async def list_all_tools(self) -> Dict[str, List[Dict]]:
        """
        获取所有服务器的工具列表
        
        Returns:
            按服务器名称分组的工具列表
        """
        all_tools = {}
        
        # 创建所有任务
        tasks = {}
        for server_name, client in self.servers.items():
            tasks[server_name] = asyncio.create_task(client.list_tools())
        
        # 等待所有任务完成，忽略异常
        for server_name, task in tasks.items():
            try:
                tools = await task
                all_tools[server_name] = tools
            except Exception as e:
                self.logger.error(f"获取服务器 {server_name} 工具列表失败: {e}")
                all_tools[server_name] = []
        
        return all_tools
    
    async def call_tool_by_server(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """
        在指定服务器上调用工具
        
        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        if server_name not in self.servers:
            raise ValueError(f"服务器 '{server_name}' 不存在")
        
        return await self.servers[server_name].call_tool(tool_name, arguments)
    
    async def find_and_call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """
        自动查找并调用工具（在所有服务器中搜索）
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        try:
            # 先查找工具在哪个服务器上
            all_tools = await self.list_all_tools()
            target_server = None
            
            for server_name, tools in all_tools.items():
                for tool in tools:
                    if tool['name'] == tool_name:
                        target_server = server_name
                        break
                if target_server:
                    break
            
            if not target_server:
                available_tools = []
                for tools in all_tools.values():
                    available_tools.extend([tool['name'] for tool in tools])
                raise Exception(f"工具 '{tool_name}' 在所有服务器中都不存在。可用工具: {available_tools}")
            
            # 调用工具
            return await self.call_tool_by_server(target_server, tool_name, arguments)
            
        except Exception as e:
            self.logger.error(f"查找和调用工具失败: {e}")
            raise
    
    def get_server_names(self) -> List[str]:
        """获取所有服务器名称"""
        return list(self.servers.keys())
    
    def get_client(self, server_name: str) -> Optional[UniversalMCPClient]:
        """获取指定服务器的客户端"""
        return self.servers.get(server_name)
    
    # 同步包装方法
    def list_all_tools_sync(self) -> Dict[str, List[Dict]]:
        """同步获取所有工具列表"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.list_all_tools())
                    return future.result()
            else:
                return asyncio.run(self.list_all_tools())
        except RuntimeError:
            return asyncio.run(self.list_all_tools())
    
    def call_tool_by_server_sync(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """同步调用指定服务器的工具"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.call_tool_by_server(server_name, tool_name, arguments))
                    return future.result()
            else:
                return asyncio.run(self.call_tool_by_server(server_name, tool_name, arguments))
        except RuntimeError:
            return asyncio.run(self.call_tool_by_server(server_name, tool_name, arguments))
    
    def find_and_call_tool_sync(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """同步自动查找并调用工具"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建一个任务
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.find_and_call_tool(tool_name, arguments))
                    return future.result()
            else:
                return asyncio.run(self.find_and_call_tool(tool_name, arguments))
        except RuntimeError:
            return asyncio.run(self.find_and_call_tool(tool_name, arguments)) 