#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OPS Agent 主入口 - 统一的 AI 自动化助手
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from ai_workflow_controller import AIWorkflowController


class OpsAgent:
    """OPS Agent - 智能运维自动化助手"""
    
    def __init__(self, config_file: str = 'mcp_config.json'):
        """
        初始化 OPS Agent
        
        Args:
            config_file: MCP配置文件路径
        """
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化工作流控制器
        self.workflow_controller = AIWorkflowController(config_file)
        
        self.logger.info("🤖 OPS Agent 初始化完成")
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        try:
            tools_summary = self.workflow_controller.get_available_tools_summary()
            workflows = self.workflow_controller.list_workflows()
            
            status = {
                "status": "running",
                "timestamp": datetime.now().isoformat(),
                "available_servers": list(tools_summary.keys()),
                "total_tools": sum(len(tools) for tools in tools_summary.values()),
                "available_workflows": workflows,
                "tools_by_server": tools_summary
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def list_all_tools(self) -> List[Dict]:
        """列出所有可用工具"""
        return self.workflow_controller.get_all_available_tools()
    
    def suggest_workflow(self, user_request: str) -> List[str]:
        """根据用户请求建议工作流"""
        return self.workflow_controller.ai_suggest_workflow(user_request)
    
    def suggest_tools(self, user_request: str) -> List[Dict]:
        """根据用户请求建议工具"""
        return self.workflow_controller.ai_suggest_tools(user_request)
    
    def execute_workflow(self, workflow_name: str, params: Optional[Dict] = None) -> Dict:
        """执行工作流"""
        try:
            result = self.workflow_controller.execute_workflow_sync(workflow_name, params)
            self.logger.info(f"工作流 {workflow_name} 执行完成")
            return result
        except Exception as e:
            self.logger.error(f"执行工作流失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """直接调用工具"""
        try:
            result = self.workflow_controller.call_tool_directly_sync(tool_name, arguments)
            self.logger.info(f"工具 {tool_name} 调用完成")
            return result
        except Exception as e:
            self.logger.error(f"调用工具失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_tool_info(self, tool_name: str) -> Optional[Dict]:
        """获取工具详细信息"""
        return self.workflow_controller.get_tool_parameters(tool_name)
    
    def create_custom_workflow(self, user_request: str) -> Dict:
        """根据用户请求创建自定义工作流"""
        tools_summary = self.workflow_controller.get_available_tools_summary()
        return self.workflow_controller.ai_generate_custom_workflow(user_request, tools_summary)
    
    def process_user_request(self, user_request: str, auto_execute: bool = False) -> Dict:
        """
        处理用户请求 - AI 智能决策
        
        Args:
            user_request: 用户请求描述
            auto_execute: 是否自动执行建议的操作
            
        Returns:
            处理结果
        """
        self.logger.info(f"处理用户请求: {user_request}")
        
        try:
            # 1. 分析用户请求
            suggested_workflows = self.suggest_workflow(user_request)
            suggested_tools = self.suggest_tools(user_request)
            
            response = {
                "user_request": user_request,
                "analysis": {
                    "suggested_workflows": suggested_workflows,
                    "suggested_tools": [tool['name'] for tool in suggested_tools[:5]],
                },
                "recommendations": [],
                "executed_actions": [],
                "timestamp": datetime.now().isoformat()
            }
            
            # 2. 生成推荐
            if suggested_workflows:
                for workflow in suggested_workflows:
                    workflow_info = self.workflow_controller.get_workflow_info(workflow)
                    response["recommendations"].append({
                        "type": "workflow",
                        "name": workflow,
                        "description": workflow_info.get('description', ''),
                        "confidence": 0.8
                    })
            
            if suggested_tools:
                for tool in suggested_tools[:3]:
                    response["recommendations"].append({
                        "type": "tool",
                        "name": tool['name'],
                        "description": tool['description'],
                        "server": tool['server'],
                        "confidence": 0.6
                    })
            
            # 3. 自动执行（如果启用）
            if auto_execute and suggested_workflows:
                workflow_name = suggested_workflows[0]
                self.logger.info(f"自动执行工作流: {workflow_name}")
                
                execution_result = self.execute_workflow(workflow_name)
                response["executed_actions"].append({
                    "type": "workflow_execution",
                    "workflow": workflow_name,
                    "result": execution_result
                })
            
            return response
            
        except Exception as e:
            self.logger.error(f"处理用户请求失败: {e}")
            return {
                "user_request": user_request,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


def main():
    """主函数 - 演示 OPS Agent 功能"""
    try:
        print("🤖 === OPS Agent 智能运维助手 ===")
        
        # 创建 OPS Agent
        agent = OpsAgent()
        
        # 显示系统状态
        print(f"\n📊 系统状态:")
        status = agent.get_system_status()
        print(f"  状态: {status['status']}")
        print(f"  可用服务器: {status['available_servers']}")
        print(f"  工具总数: {status['total_tools']}")
        print(f"  可用工作流: {status['available_workflows']}")
        
        # 演示智能请求处理
        test_requests = [
            "我想看看TAPD项目的进展情况",
            "检查有没有超期的任务",
            "发送一条微信通知",
            "获取工作空间信息"
        ]
        
        for request in test_requests:
            print(f"\n🔍 处理请求: {request}")
            response = agent.process_user_request(request)
            
            if "analysis" in response:
                analysis = response["analysis"]
                print(f"  建议工作流: {analysis['suggested_workflows']}")
                print(f"  建议工具: {analysis['suggested_tools']}")
            
            if response.get("recommendations"):
                print(f"  推荐操作:")
                for rec in response["recommendations"][:2]:
                    print(f"    - {rec['type']}: {rec['name']} ({rec['confidence']*100:.0f}%)")
        
        print(f"\n✅ OPS Agent 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        logging.error(f"演示失败: {e}")


if __name__ == "__main__":
    main() 