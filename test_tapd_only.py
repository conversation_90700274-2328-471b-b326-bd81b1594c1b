#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化TAPD测试脚本 - 只测试TAPD功能
"""

import asyncio
import json
from datetime import datetime
from mcp_universal_client import UniversalMCPClient


class SimpleTAPDTester:
    """简化TAPD测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.client = UniversalMCPClient(server_name="mcp-server-tapd")
        
    async def test_create_story(self):
        """测试创建TAPD需求"""
        print("🧪 测试创建TAPD需求")
        print("=" * 50)
        
        # 创建测试需求参数
        story_params = {
            "workspace_id": 37936651,
            "name": f"OCR测试需求-{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": "这是一个测试需求，用于验证TAPD MCP服务器的功能。",
            "priority": "高",
            "status": "新建"
        }
        
        try:
            print(f"📋 创建需求参数:")
            print(f"   workspace_id: {story_params['workspace_id']}")
            print(f"   name: {story_params['name']}")
            print(f"   priority: {story_params['priority']}")
            
            print(f"\n🔄 开始创建需求...")
            result = await self.client.call_tool("create_story_or_task", story_params)
            
            print(f"✅ 需求创建成功!")
            print(f"   结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
            
        except Exception as e:
            print(f"❌ 需求创建失败: {e}")
            return None
    
    async def test_get_workspace_info(self):
        """测试获取工作空间信息"""
        print("\n🧪 测试获取工作空间信息")
        print("=" * 50)
        
        try:
            print(f"🔄 获取工作空间 37936651 的信息...")
            result = await self.client.call_tool("get_workspace_info", {"workspace_id": 37936651})
            
            print(f"✅ 工作空间信息获取成功!")
            print(f"   结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
            
        except Exception as e:
            print(f"❌ 工作空间信息获取失败: {e}")
            return None
    
    async def test_list_tools(self):
        """测试工具列表"""
        print("\n🧪 测试获取TAPD工具列表")
        print("=" * 50)
        
        try:
            print(f"🔄 获取工具列表...")
            tools = await self.client.list_tools()
            
            print(f"✅ 工具列表获取成功! 共 {len(tools)} 个工具:")
            for i, tool in enumerate(tools[:10]):  # 只显示前10个
                print(f"   {i+1}. {tool['name']}: {tool['description'][:50]}...")
            
            if len(tools) > 10:
                print(f"   ... 还有 {len(tools) - 10} 个工具")
            
            return tools
            
        except Exception as e:
            print(f"❌ 工具列表获取失败: {e}")
            return None

    async def run_test(self):
        """运行所有测试"""
        print("🚀 开始TAPD简化测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # 1. 测试工具列表
        tools = await self.test_list_tools()
        results['tools'] = tools is not None
        
        # 2. 测试工作空间信息
        workspace_info = await self.test_get_workspace_info()
        results['workspace_info'] = workspace_info is not None
        
        # 3. 测试创建需求
        story_result = await self.test_create_story()
        results['create_story'] = story_result is not None
        
        # 打印测试总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结")
        print("=" * 50)
        
        for test_name, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        all_success = all(results.values())
        print(f"\n🎯 总体结果: {'✅ 全部成功' if all_success else '❌ 有失败项目'}")
        
        return results


async def main():
    """主函数"""
    tester = SimpleTAPDTester()
    await tester.run_test()


if __name__ == "__main__":
    asyncio.run(main()) 