#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIOps 系统启动脚本 - 统一启动和管理整个智能运维系统
"""

import asyncio
import signal
import sys
import logging
from datetime import datetime
from aiops_agent import AIOpsAgent


class AIOpsLauncher:
    """AIOps 系统启动器"""
    
    def __init__(self):
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('aiops_system.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.agent = None
        self.is_running = False
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器 - 优雅关闭"""
        self.logger.info(f"收到信号 {signum}，开始优雅关闭...")
        self.is_running = False
        
        if self.agent:
            self.agent.stop_monitoring()
        
        sys.exit(0)
    
    async def start_system(self):
        """启动 AIOps 系统"""
        try:
            print("=" * 60)
            print("🚀 启动 AIOps 智能运维系统")
            print("=" * 60)
            
            # 创建 AIOps Agent
            print("📝 初始化 AIOps Agent...")
            self.agent = AIOpsAgent()
            
            # 检查系统状态
            print("🔍 检查系统状态...")
            dashboard = self.agent.get_dashboard_data()
            
            print(f"✅ 系统状态: {dashboard['system_status']}")
            print(f"📊 数据源: {', '.join(dashboard['data_sources'])}")
            print(f"📈 监控规则: {self.agent.anomaly_detector.get_status()['enabled_rules']} 个")
            
            # 检查 vLLM 连接
            print("🤖 检查 vLLM 连接...")
            vllm_health = self.agent.vllm_client.health_check()
            
            if vllm_health["status"] == "healthy":
                print("✅ vLLM 服务正常")
                models = vllm_health.get("models", [])
                if models:
                    print(f"   可用模型: {len(models)} 个")
            else:
                print(f"⚠️ vLLM 服务异常: {vllm_health.get('error', '未知错误')}")
                print("   系统将继续运行，但 AI 分析功能可能受限")
            
            # 检查工作流控制器
            print("🔧 检查工作流控制器...")
            try:
                tools_summary = self.agent.workflow_controller.get_available_tools_summary()
                total_tools = sum(len(tools) for tools in tools_summary.values())
                print(f"✅ 工作流控制器正常，可用工具: {total_tools} 个")
                
                for server, tools in tools_summary.items():
                    print(f"   {server}: {len(tools)} 个工具")
                    
            except Exception as e:
                print(f"⚠️ 工作流控制器异常: {e}")
                print("   部分通知功能可能受限")
            
            print("\n" + "=" * 60)
            print("🎯 开始监控...")
            print("=" * 60)
            
            self.is_running = True
            
            # 启动监控
            monitoring_task = asyncio.create_task(self.agent.start_monitoring())
            
            # 启动状态报告
            status_task = asyncio.create_task(self._periodic_status_report())
            
            # 等待任务完成
            await asyncio.gather(monitoring_task, status_task)
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            raise
    
    async def _periodic_status_report(self):
        """定期状态报告"""
        report_interval = 300  # 5分钟
        
        while self.is_running:
            try:
                await asyncio.sleep(report_interval)
                
                if not self.is_running:
                    break
                
                # 获取系统状态
                dashboard = self.agent.get_dashboard_data()
                overview = dashboard["overview"]
                
                # 输出状态报告
                print(f"\n📊 === 系统状态报告 ({datetime.now().strftime('%H:%M:%S')}) ===")
                print(f"🚨 24小时异常: {overview['total_anomalies_24h']} 个")
                print(f"🔥 关键异常: {overview['critical_anomalies']} 个")
                print(f"⚡ 活跃任务: {overview['active_tasks']} 个")
                print(f"✅ 完成任务: {overview['completed_tasks']} 个")
                
                # 显示最近异常
                recent_anomalies = dashboard["recent_anomalies"]
                if recent_anomalies:
                    print(f"📋 最近异常:")
                    for anomaly in recent_anomalies[-3:]:  # 显示最近3个
                        severity_icon = {
                            "critical": "🔴",
                            "high": "🟠", 
                            "medium": "🟡",
                            "low": "🟢"
                        }.get(anomaly["severity"], "⚪")
                        
                        print(f"   {severity_icon} {anomaly['metric_name']}: {anomaly['description'][:50]}...")
                
                print("=" * 50)
                
            except Exception as e:
                self.logger.error(f"状态报告失败: {e}")
    
    def run(self):
        """运行 AIOps 系统"""
        try:
            asyncio.run(self.start_system())
        except KeyboardInterrupt:
            print("\n👋 用户中断，系统已停止")
        except Exception as e:
            print(f"❌ 系统运行失败: {e}")
            self.logger.error(f"系统运行失败: {e}")
            sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = """
🤖 AIOps 智能运维系统

用法:
    python start_aiops.py [选项]

选项:
    --help, -h      显示此帮助信息
    --demo          运行演示模式
    --test-vllm     测试 vLLM 连接
    --test-mcp      测试 MCP 连接
    --config        显示配置信息

系统组件:
    • 异常检测模块    - 监控业务数据，识别异常模式
    • AI 分析引擎     - 使用 vLLM 大模型分析异常原因
    • 工作流调度器    - 自动化响应和通知流程
    • 多渠道通知      - TAPD、微信等多种通知方式

配置文件:
    • aiops_config.json     - AIOps 主配置
    • mcp_config.json       - MCP 服务器配置
    • anomaly_config.json   - 异常检测规则配置

日志文件:
    • aiops_system.log      - 系统运行日志

更多信息请参考: README.md
    """
    print(help_text)


async def demo_mode():
    """演示模式"""
    print("🎬 === AIOps 演示模式 ===")
    
    # 创建 agent
    agent = AIOpsAgent()
    
    # 显示系统状态
    dashboard = agent.get_dashboard_data()
    print(f"系统状态: {dashboard['system_status']}")
    print(f"数据源: {dashboard['data_sources']}")
    
    # 模拟多个异常
    print("\n📊 模拟异常场景...")
    
    test_scenarios = [
        {"metric": "cpu_usage", "value": 95.0, "scenario": "CPU 使用率过高"},
        {"metric": "memory_usage", "value": 92.0, "scenario": "内存使用率告警"},
        {"metric": "response_time", "value": 3500.0, "scenario": "响应时间异常"},
        {"metric": "error_rate", "value": 8.5, "scenario": "错误率激增"}
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n{i}. 模拟场景: {scenario['scenario']}")
        
        anomaly = agent.anomaly_detector.detect_anomaly(
            metric_name=scenario["metric"],
            value=scenario["value"],
            source="demo"
        )
        
        if anomaly:
            print(f"   ✅ 触发异常: {anomaly.description}")
            print(f"   🎯 严重程度: {anomaly.severity.value}")
        else:
            print(f"   ℹ️ 未触发异常阈值")
        
        await asyncio.sleep(1)
    
    # 等待任务处理
    print("\n⏳ 等待任务处理完成...")
    await asyncio.sleep(5)
    
    # 显示最终结果
    final_dashboard = agent.get_dashboard_data()
    print(f"\n📈 演示结果:")
    print(f"活跃任务: {final_dashboard['overview']['active_tasks']}")
    print(f"完成任务: {final_dashboard['overview']['completed_tasks']}")
    print(f"检测到异常: {final_dashboard['overview']['total_anomalies_24h']}")
    
    print("\n✨ 演示完成！")


def main():
    """主入口函数"""
    if len(sys.argv) > 1:
        arg = sys.argv[1]
        
        if arg in ['--help', '-h']:
            show_help()
            return
        elif arg == '--demo':
            asyncio.run(demo_mode())
            return
        elif arg == '--test-vllm':
            from vllm_client import test_vllm_client
            test_vllm_client()
            return
        elif arg == '--test-mcp':
            from ai_workflow_controller import AIWorkflowController
            controller = AIWorkflowController()
            tools = controller.get_available_tools_summary()
            print("可用 MCP 工具:")
            for server, tool_list in tools.items():
                print(f"  {server}: {len(tool_list)} 个工具")
            return
        elif arg == '--config':
            print("配置文件状态:")
            import os
            config_files = [
                'aiops_config.json',
                'mcp_config.json', 
                'anomaly_config.json'
            ]
            for config_file in config_files:
                status = "✅ 存在" if os.path.exists(config_file) else "❌ 不存在"
                print(f"  {config_file}: {status}")
            return
        else:
            print(f"未知选项: {arg}")
            print("使用 --help 查看帮助信息")
            return
    
    # 默认启动系统
    launcher = AIOpsLauncher()
    launcher.run()


if __name__ == "__main__":
    main() 