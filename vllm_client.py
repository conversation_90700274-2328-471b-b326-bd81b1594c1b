#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
vLLM 大模型客户端 - 提供 AI 分析和决策能力
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import aiohttp
import requests
from dataclasses import dataclass


@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    task_id: str
    analysis_type: str
    findings: List[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]
    confidence_score: float
    timestamp: str
    raw_response: str


class VLLMClient:
    """vLLM 大模型客户端"""
    
    def __init__(self, 
                 base_url: str = "http://localhost:8000/v1",
                 model_name: str = "qwen2.5-7b-instruct",
                 timeout: int = 300):
        """
        初始化 vLLM 客户端
        
        Args:
            base_url: vLLM 服务器地址
            model_name: 模型名称
            timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip('/')
        self.model_name = model_name
        self.timeout = timeout
        
        # 设置日志
        self.logger = logging.getLogger(__name__)
        
        # 预定义的分析模板
        self.analysis_templates = {
            "anomaly_analysis": {
                "system_prompt": """你是一个专业的IT运维分析师。请分析以下异常数据，并提供结构化的分析结果。

分析要求：
1. 识别异常的根本原因
2. 评估影响范围和严重程度
3. 提供具体的解决建议
4. 给出置信度评分 (0-1)

请以JSON格式返回结果，包含以下字段：
- findings: 发现的问题列表
- recommendations: 推荐的解决方案
- severity: 严重程度 (low/medium/high/critical)
- confidence: 置信度分数
- next_actions: 建议的下一步行动""",
                "user_template": "异常数据：\n{data}\n\n请进行分析："
            },
            
            "performance_analysis": {
                "system_prompt": """你是一个性能分析专家。请分析以下性能数据，识别瓶颈和优化机会。

分析目标：
1. 识别性能瓶颈
2. 分析趋势和模式
3. 提供优化建议
4. 预测未来性能问题

返回JSON格式结果，包含：
- bottlenecks: 发现的瓶颈
- trends: 性能趋势分析
- optimizations: 优化建议
- predictions: 性能预测
- confidence: 分析置信度""",
                "user_template": "性能数据：\n{data}\n\n请分析性能瓶颈和优化机会："
            },
            
            "business_impact": {
                "system_prompt": """你是一个业务影响分析师。请评估技术问题对业务的潜在影响。

评估维度：
1. 业务连续性影响
2. 用户体验影响
3. 财务影响评估
4. 合规风险评估

返回JSON格式，包含：
- business_continuity: 业务连续性评估
- user_impact: 用户影响分析
- financial_impact: 财务影响估算
- compliance_risks: 合规风险
- priority: 优先级建议""",
                "user_template": "问题描述：\n{data}\n\n请评估业务影响："
            },
            
            "trend_prediction": {
                "system_prompt": """你是一个数据科学家，专门进行趋势预测和异常预警。

预测任务：
1. 分析历史数据趋势
2. 识别潜在风险点
3. 预测未来发展趋势
4. 提供预警建议

返回JSON格式：
- trends: 趋势分析
- risk_points: 风险点识别
- predictions: 未来预测
- alerts: 预警建议
- confidence: 预测可信度""",
                "user_template": "历史数据：\n{data}\n\n请进行趋势预测："
            }
        }
    
    def _get_completion_endpoint(self) -> str:
        """获取补全接口地址"""
        return f"{self.base_url}/chat/completions"
    
    def _prepare_messages(self, analysis_type: str, data: Union[str, Dict]) -> List[Dict]:
        """准备聊天消息"""
        if analysis_type not in self.analysis_templates:
            raise ValueError(f"不支持的分析类型: {analysis_type}")
        
        template = self.analysis_templates[analysis_type]
        
        # 数据格式化
        if isinstance(data, dict):
            data_str = json.dumps(data, ensure_ascii=False, indent=2)
        else:
            data_str = str(data)
        
        messages = [
            {
                "role": "system",
                "content": template["system_prompt"]
            },
            {
                "role": "user", 
                "content": template["user_template"].format(data=data_str)
            }
        ]
        
        return messages
    
    def _extract_json_from_response(self, response_text: str) -> Dict:
        """从响应中提取JSON"""
        try:
            # 尝试直接解析整个响应
            return json.loads(response_text)
        except json.JSONDecodeError:
            # 尝试提取JSON代码块
            if "```json" in response_text:
                start = response_text.find("```json") + 7
                end = response_text.find("```", start)
                if end != -1:
                    json_str = response_text[start:end].strip()
                    try:
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        pass
            
            # 如果无法提取JSON，返回原始文本
            return {
                "raw_response": response_text,
                "findings": ["无法解析结构化结果"],
                "recommendations": ["请检查模型输出格式"],
                "confidence": 0.1
            }
    
    async def analyze_async(self,
                           analysis_type: str,
                           data: Union[str, Dict],
                           task_id: Optional[str] = None) -> AnalysisResult:
        """异步分析方法"""
        if task_id is None:
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            messages = self._prepare_messages(analysis_type, data)
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": 0.3,
                "max_tokens": 2048,
                "stream": False
            }
            
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                async with session.post(
                    self._get_completion_endpoint(),
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"vLLM API 错误 {response.status}: {error_text}")
                    
                    result = await response.json()
                    
            # 提取响应内容
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
            else:
                raise Exception("vLLM 响应格式错误")
            
            # 解析结构化结果
            parsed_result = self._extract_json_from_response(content)
            
            # 构建分析结果
            analysis_result = AnalysisResult(
                task_id=task_id,
                analysis_type=analysis_type,
                findings=parsed_result.get("findings", []),
                recommendations=parsed_result.get("recommendations", []),
                confidence_score=parsed_result.get("confidence", 0.5),
                timestamp=datetime.now().isoformat(),
                raw_response=content
            )
            
            self.logger.info(f"分析任务 {task_id} 完成，类型: {analysis_type}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析任务 {task_id} 失败: {e}")
            
            # 返回错误结果
            return AnalysisResult(
                task_id=task_id,
                analysis_type=analysis_type,
                findings=[f"分析失败: {str(e)}"],
                recommendations=["请检查vLLM服务状态"],
                confidence_score=0.0,
                timestamp=datetime.now().isoformat(),
                raw_response=str(e)
            )
    
    def analyze_sync(self,
                    analysis_type: str,
                    data: Union[str, Dict],
                    task_id: Optional[str] = None) -> AnalysisResult:
        """同步分析方法"""
        if task_id is None:
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            messages = self._prepare_messages(analysis_type, data)
            
            payload = {
                "model": self.model_name,
                "messages": messages,
                "temperature": 0.3,
                "max_tokens": 2048,
                "stream": False
            }
            
            response = requests.post(
                self._get_completion_endpoint(),
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise Exception(f"vLLM API 错误 {response.status_code}: {response.text}")
            
            result = response.json()
            
            # 提取响应内容
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
            else:
                raise Exception("vLLM 响应格式错误")
            
            # 解析结构化结果
            parsed_result = self._extract_json_from_response(content)
            
            # 构建分析结果
            analysis_result = AnalysisResult(
                task_id=task_id,
                analysis_type=analysis_type,
                findings=parsed_result.get("findings", []),
                recommendations=parsed_result.get("recommendations", []),
                confidence_score=parsed_result.get("confidence", 0.5),
                timestamp=datetime.now().isoformat(),
                raw_response=content
            )
            
            self.logger.info(f"分析任务 {task_id} 完成，类型: {analysis_type}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析任务 {task_id} 失败: {e}")
            
            # 返回错误结果
            return AnalysisResult(
                task_id=task_id,
                analysis_type=analysis_type,
                findings=[f"分析失败: {str(e)}"],
                recommendations=["请检查vLLM服务状态"],
                confidence_score=0.0,
                timestamp=datetime.now().isoformat(),
                raw_response=str(e)
            )
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            response = requests.get(
                f"{self.base_url}/models",
                timeout=10
            )
            
            if response.status_code == 200:
                models = response.json()
                return {
                    "status": "healthy",
                    "models": models.get("data", []),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "unhealthy", 
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_supported_analysis_types(self) -> List[str]:
        """获取支持的分析类型"""
        return list(self.analysis_templates.keys())


def test_vllm_client():
    """测试 vLLM 客户端"""
    client = VLLMClient()
    
    # 健康检查
    print("=== vLLM 健康检查 ===")
    health = client.health_check()
    print(json.dumps(health, ensure_ascii=False, indent=2))
    
    if health["status"] == "healthy":
        # 测试异常分析
        print("\n=== 测试异常分析 ===")
        test_data = {
            "metric": "cpu_usage",
            "value": 95.5,
            "threshold": 80.0,
            "timestamp": "2024-01-10T14:30:00Z",
            "host": "web-server-01",
            "context": "网站响应时间增加，用户投诉增多"
        }
        
        result = client.analyze_sync("anomaly_analysis", test_data)
        print(f"任务ID: {result.task_id}")
        print(f"发现的问题: {result.findings}")
        print(f"推荐方案: {result.recommendations}")
        print(f"置信度: {result.confidence_score}")


if __name__ == "__main__":
    test_vllm_client() 