# OPS Agent - 智能运维自动化助手

基于 MCP (Model Context Protocol) 的智能运维助手，能够自动化处理 TAPD 项目管理、微信通知等运维任务。

## 🚀 功能特性

### 核心 AIOps 工作流
- **异常检测模块**：实时监控业务数据，自动识别异常模式和趋势
- **AI 分析引擎**：集成 vLLM 大模型，智能分析异常原因和业务影响
- **自动化调度**：根据异常严重程度自动触发相应的响应流程
- **多渠道通知**：支持 TAPD、微信等多种通知方式
- **运营反馈闭环**：支持运营人员反馈，持续优化分析质量

### 传统功能增强
- **智能工作流**：根据用户请求自动推荐和执行相应的运维操作
- **多服务器支持**：统一管理多个 MCP 服务器（TAPD、微信机器人等）
- **AI 辅助决策**：智能分析用户需求，建议最合适的工具和流程
- **预定义工作流**：内置常用运维场景的自动化流程
- **动态工具发现**：自动发现和加载 MCP 服务器提供的工具

## 📁 项目结构

```
ops_agent2/
├── start_aiops.py              # 🚀 AIOps 系统启动脚本
├── aiops_agent.py              # 🤖 核心 AIOps 智能代理
├── anomaly_detector.py         # 🔍 异常检测模块
├── vllm_client.py             # 🧠 vLLM 大模型客户端
├── ai_workflow_controller.py   # ⚡ AI 工作流控制器
├── mcp_universal_client.py     # 🔗 通用 MCP 客户端
├── ops_agent_main.py          # 📱 传统接口（向后兼容）
├── mcp_config.json            # ⚙️ MCP 服务器配置
├── aiops_config.json          # 🎛️ AIOps 主配置
├── anomaly_config.json        # 📊 异常检测规则配置
├── tapd_mcp/                  # 📋 TAPD MCP 服务器
│   ├── main.py
│   ├── requirements.txt
│   └── ...
└── wechat_bot_mcp/           # 💬 微信机器人 MCP 服务器
    ├── main.py
    ├── start.py
    └── ...
```

## ⚡ 快速开始

### 1. 安装依赖

```bash
# 安装 TAPD MCP 服务器
cd tapd_mcp
uv sync

# 安装微信机器人 MCP 服务器
cd ../wechat_bot_mcp
uv sync
```

### 2. 配置环境

编辑 `mcp_config.json` 文件，配置您的 TAPD 和微信机器人凭据：

```json
{
  "mcpServers": {
    "mcp-server-tapd": {
      "command": "uv",
      "args": ["run", "--directory", "tapd_mcp", "mcp-server-tapd"],
      "env": {
        "TAPD_ACCESS_TOKEN": "您的个人Token",
        "TAPD_API_USER": "您的API用户ID",
        "TAPD_API_PASSWORD": "您的API密码"
      }
    }
  }
}
```

### 3. 运行 OPS Agent

```bash
python ops_agent_main.py
```

## 🤖 使用示例

### 智能请求处理

```python
from ops_agent_main import OpsAgent

# 创建 OPS Agent
agent = OpsAgent()

# 处理自然语言请求
response = agent.process_user_request("检查有没有超期的TAPD任务")

# 执行预定义工作流
result = agent.execute_workflow("tapd_daily_report")

# 直接调用工具
tool_result = agent.call_tool("get_stories_or_tasks", {
    "workspace_id": "12345",
    "status": "processing"
})
```

### 可用工作流

- `tapd_daily_report`: TAPD 每日报告
- `tapd_overdue_check`: 超期任务检查
- `wechat_notification`: 微信消息通知

## 🔧 配置说明

### MCP 服务器配置

在 `mcp_config.json` 中配置各个 MCP 服务器：

- **command**: 启动命令
- **args**: 命令参数
- **env**: 环境变量（API 密钥、URL 等）

### 环境变量

#### TAPD 配置
- `TAPD_ACCESS_TOKEN`: TAPD 个人访问令牌
- `TAPD_API_USER`: TAPD API 用户ID  
- `TAPD_API_PASSWORD`: TAPD API 密码
- `TAPD_API_BASE_URL`: TAPD API 基础URL
- `TAPD_BASE_URL`: TAPD 基础URL

#### 微信机器人配置
- `BOT_URL`: 微信机器人 Webhook URL

## 🎯 主要组件

### AIWorkflowController
- 预定义工作流管理
- AI 智能建议
- 工具调用协调

### UniversalMCPClient  
- MCP 服务器连接
- 工具发现和调用
- 异步/同步接口

### OpsAgent
- 统一用户接口
- 智能请求处理
- 系统状态监控

## 🚧 开发计划

- [ ] 添加更多预定义工作流
- [ ] 支持工作流的可视化编辑
- [ ] 增加Web UI界面
- [ ] 支持定时任务调度
- [ ] 添加监控和告警功能

## 📄 许可证

本项目基于 MIT 许可证开源。 