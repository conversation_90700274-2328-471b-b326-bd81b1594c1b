#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 工作流控制器 - 智能调用 MCP 工具实现自动化流程
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from mcp_universal_client import MCPMultiServerClient


class AIWorkflowController:
    """AI 工作流控制器 - 根据预设规则和 AI 决策调用 MCP 工具"""
    
    def __init__(self, mcp_config_file: str = 'mcp_config.json', workflow_config_file: str = 'workflow_config.json'):
        """
        初始化 AI 工作流控制器
        
        Args:
            mcp_config_file: MCP配置文件路径
            workflow_config_file: 工作流配置文件路径
        """
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.mcp_client = MCPMultiServerClient(mcp_config_file)
        self.workflow_config_file = workflow_config_file
        self.workflow_config = self._load_workflow_config()
        
        # 可用工具缓存
        self.available_tools = {}
        
        try:
            self._cache_available_tools()
        except Exception as e:
            self.logger.error(f"初始化工具缓存失败: {e}")
            self.available_tools = {}
        
        self.logger.info("✅ AI 工作流控制器初始化完成")
    
    def _load_workflow_config(self) -> Dict:
        """加载工作流配置文件"""
        try:
            with open(self.workflow_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"工作流配置文件 {self.workflow_config_file} 未找到，使用默认配置")
            return self._get_default_workflow_config()
        except json.JSONDecodeError as e:
            self.logger.error(f"工作流配置文件格式错误: {e}")
            return self._get_default_workflow_config()
    
    def _get_default_workflow_config(self) -> Dict:
        """获取默认工作流配置"""
        return {
            "workflows": {
                "tapd_daily_report": {
                    "name": "TAPD 每日报告",
                    "description": "获取 TAPD 项目的每日进展报告",
                    "steps": [
                        {
                            "step": "get_workspace_info",
                            "tool": "get_workspace_info",
                            "description": "获取工作空间信息",
                            "required_params": ["workspace_id"],
                            "optional_params": []
                        },
                        {
                            "step": "get_active_stories",
                            "tool": "get_stories_or_tasks",
                            "description": "获取进行中的需求",
                            "required_params": ["workspace_id"],
                            "optional_params": ["options"]
                        },
                        {
                            "step": "get_bugs",
                            "tool": "get_bug",
                            "description": "获取未解决的缺陷",
                            "required_params": ["workspace_id"],
                            "optional_params": ["options"]
                        }
                    ]
                },
                "tapd_reminder_check": {
                    "name": "TAPD 超期提醒检查",
                    "description": "检查超期的需求和缺陷并发送提醒",
                    "steps": [
                        {
                            "step": "check_overdue_stories",
                            "tool": "get_stories_or_tasks",
                            "description": "检查超期需求",
                            "required_params": ["workspace_id"],
                            "optional_params": ["options"]
                        },
                        {
                            "step": "check_overdue_bugs",
                            "tool": "get_bug",
                            "description": "检查超期缺陷",
                            "required_params": ["workspace_id"],
                            "optional_params": ["options"]
                        },
                        {
                            "step": "send_reminder",
                            "tool": "send_qiwei_message",
                            "description": "发送企业微信提醒",
                            "required_params": ["message"],
                            "optional_params": ["webhook_url"]
                        }
                    ]
                },
                "wechat_notification": {
                    "name": "微信消息通知",
                    "description": "发送微信消息通知",
                    "steps": [
                        {
                            "step": "send_message",
                            "tool": "send_message",
                            "description": "发送微信消息",
                            "required_params": ["message"],
                            "optional_params": ["recipient"]
                        }
                    ]
                },
                "ocr_validation_exception_handler": {
                    "name": "OCR校验异常处理流程",
                    "description": "自动处理OCR校验不通过的异常情况",
                    "steps": [
                        {
                            "step": "create_parent_story",
                            "tool": "create_story_or_task",
                            "description": "创建TAPD父需求",
                            "required_params": ["workspace_id", "name", "description"],
                            "optional_params": ["priority", "status"]
                        },
                        {
                            "step": "create_child_story",
                            "tool": "create_story_or_task", 
                            "description": "创建TAPD子需求",
                            "required_params": ["workspace_id", "name", "description"],
                            "optional_params": ["priority", "status"]
                        },
                        {
                            "step": "send_wechat_alert",
                            "tool": "send_text_message",
                            "description": "发送企业微信告警通知",
                            "required_params": ["content"],
                            "optional_params": ["mentioned_list"]
                        }
                    ]
                }
            },
            "default_params": {
                "workspace_id": 55191012,
                "story_options": {
                    "entity_type": "stories",
                    "limit": 20
                },
                "bug_options": {
                    "entity_type": "bugs",
                    "limit": 20
                }
            }
        }
    
    def _cache_available_tools(self):
        """缓存可用工具信息"""
        try:
            self.available_tools = self.mcp_client.list_all_tools_sync()
            self.logger.info(f"已缓存 {len(self.available_tools)} 个服务器的工具信息")
        except Exception as e:
            self.logger.error(f"缓存工具信息失败: {e}")
            self.available_tools = {}
    
    def get_available_tools_summary(self) -> Dict[str, List[str]]:
        """获取可用工具概要"""
        summary = {}
        for server_name, tools in self.available_tools.items():
            summary[server_name] = [tool['name'] for tool in tools]
        return summary
    
    def get_all_available_tools(self) -> List[Dict]:
        """获取所有可用工具的详细信息"""
        all_tools = []
        for server_name, tools in self.available_tools.items():
            for tool in tools:
                tool_info = tool.copy()
                tool_info['server'] = server_name
                all_tools.append(tool_info)
        return all_tools
    
    def get_tool_description(self, tool_name: str) -> Optional[str]:
        """获取指定工具的描述"""
        for server_name, tools in self.available_tools.items():
            for tool in tools:
                if tool['name'] == tool_name:
                    return tool['description']
        return None
    
    def get_tool_parameters(self, tool_name: str) -> Optional[Dict]:
        """获取指定工具的参数信息"""
        for server_name, tools in self.available_tools.items():
            for tool in tools:
                if tool['name'] == tool_name:
                    return tool['parameters']
        return None
    
    async def execute_workflow(self, workflow_name: str, custom_params: Optional[Dict] = None) -> Dict:
        """
        执行指定的工作流
        
        Args:
            workflow_name: 工作流名称
            custom_params: 自定义参数，会覆盖默认参数
            
        Returns:
            工作流执行结果
        """
        if workflow_name not in self.workflow_config.get('workflows', {}):
            raise ValueError(f"工作流 '{workflow_name}' 不存在")
        
        workflow = self.workflow_config['workflows'][workflow_name]
        default_params = self.workflow_config.get('default_params', {})
        
        # 合并参数
        params = default_params.copy()
        if custom_params:
            params.update(custom_params)
        
        self.logger.info(f"开始执行工作流: {workflow['name']}")
        
        results = {
            'workflow_name': workflow_name,
            'start_time': datetime.now().isoformat(),
            'steps': [],
            'success': True,
            'error': None
        }
        
        try:
            for step_config in workflow['steps']:
                step_name = step_config['step']
                tool_name = step_config['tool']
                
                self.logger.info(f"执行步骤: {step_name} (工具: {tool_name})")
                
                # 准备参数
                step_params = self._prepare_step_params(step_config, params)
                
                # 执行工具
                try:
                    step_result = await self.mcp_client.find_and_call_tool(tool_name, step_params)
                    
                    step_info = {
                        'step': step_name,
                        'tool': tool_name,
                        'params': step_params,
                        'success': True,
                        'result': step_result,
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.logger.info(f"步骤 {step_name} 执行成功")
                    
                except Exception as e:
                    step_info = {
                        'step': step_name,
                        'tool': tool_name,
                        'params': step_params,
                        'success': False,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    self.logger.error(f"步骤 {step_name} 执行失败: {e}")
                
                results['steps'].append(step_info)
        
        except Exception as e:
            results['success'] = False
            results['error'] = str(e)
            self.logger.error(f"工作流执行失败: {e}")
        
        results['end_time'] = datetime.now().isoformat()
        return results
    
    def _prepare_step_params(self, step_config: Dict, global_params: Dict) -> Dict:
        """准备步骤参数"""
        step_params = {}
        
        # 添加必需参数
        for param in step_config.get('required_params', []):
            if param in global_params:
                step_params[param] = global_params[param]
            else:
                raise ValueError(f"缺少必需参数: {param}")
        
        # 添加可选参数
        for param in step_config.get('optional_params', []):
            if param in global_params:
                step_params[param] = global_params[param]
        
        # 特殊处理 options 参数
        if 'options' in step_params and step_config['tool'] == 'get_stories_or_tasks':
            if 'story_options' in global_params:
                step_params['options'] = global_params['story_options']
        elif 'options' in step_params and step_config['tool'] == 'get_bug':
            if 'bug_options' in global_params:
                step_params['options'] = global_params['bug_options']
        
        return step_params
    
    def ai_suggest_workflow(self, user_request: str) -> List[str]:
        """
        AI 建议适合的工作流
        
        Args:
            user_request: 用户请求描述
            
        Returns:
            建议的工作流名称列表
        """
        suggestions = []
        
        # 简单的关键词匹配逻辑
        request_lower = user_request.lower()
        
        if any(keyword in request_lower for keyword in ['报告', 'report', '进展', '状态']):
            suggestions.append('tapd_daily_report')
        
        if any(keyword in request_lower for keyword in ['提醒', 'reminder', '超期', '过期', '逾期']):
            suggestions.append('tapd_reminder_check')
            
        if any(keyword in request_lower for keyword in ['微信', 'wechat', '消息', 'message', '通知']):
            suggestions.append('wechat_notification')
        
        return suggestions
    
    def ai_suggest_tools(self, user_request: str) -> List[Dict]:
        """
        AI 建议适合的工具
        
        Args:
            user_request: 用户请求描述
            
        Returns:
            建议的工具列表
        """
        suggestions = []
        request_lower = user_request.lower()
        
        # 获取所有可用工具
        all_tools = self.get_all_available_tools()
        
        # 基于关键词匹配工具
        for tool in all_tools:
            tool_desc_lower = tool['description'].lower()
            tool_name_lower = tool['name'].lower()
            
            # 检查工具名称和描述是否包含相关关键词
            if any(keyword in tool_name_lower or keyword in tool_desc_lower 
                   for keyword in request_lower.split()):
                suggestions.append(tool)
        
        # 限制返回数量
        return suggestions[:10]
    
    def ai_generate_custom_workflow(self, user_request: str, available_tools: Dict[str, List[str]]) -> Dict:
        """
        AI 生成自定义工作流
        
        Args:
            user_request: 用户请求描述
            available_tools: 可用工具列表
            
        Returns:
            生成的工作流配置
        """
        workflow = {
            "name": f"自定义工作流 - {user_request[:20]}",
            "description": f"根据用户请求生成: {user_request}",
            "steps": []
        }
        
        # 简单的工具选择逻辑
        if "工作空间" in user_request or "项目信息" in user_request:
            workflow["steps"].append({
                "step": "get_workspace_info",
                "tool": "get_workspace_info",
                "description": "获取工作空间信息",
                "required_params": ["workspace_id"],
                "optional_params": []
            })
        
        if "需求" in user_request or "故事" in user_request:
            workflow["steps"].append({
                "step": "get_stories",
                "tool": "get_stories_or_tasks",
                "description": "获取需求信息",
                "required_params": ["workspace_id"],
                "optional_params": ["options"]
            })
        
        if "缺陷" in user_request or "bug" in user_request.lower():
            workflow["steps"].append({
                "step": "get_bugs",
                "tool": "get_bug",
                "description": "获取缺陷信息",
                "required_params": ["workspace_id"],
                "optional_params": ["options"]
            })
        
        if "微信" in user_request or "消息" in user_request:
            workflow["steps"].append({
                "step": "send_notification",
                "tool": "send_message",
                "description": "发送消息通知",
                "required_params": ["message"],
                "optional_params": ["recipient"]
            })
        
        return workflow
    
    def execute_workflow_sync(self, workflow_name: str, custom_params: Optional[Dict] = None) -> Dict:
        """同步执行工作流"""
        return asyncio.run(self.execute_workflow(workflow_name, custom_params))
    
    def list_workflows(self) -> List[str]:
        """列出所有可用的工作流"""
        return list(self.workflow_config.get('workflows', {}).keys())
    
    def get_workflow_info(self, workflow_name: str) -> Optional[Dict]:
        """获取工作流信息"""
        return self.workflow_config.get('workflows', {}).get(workflow_name)
    
    async def call_tool_directly(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """
        直接调用指定工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        return await self.mcp_client.find_and_call_tool(tool_name, arguments)
    
    def call_tool_directly_sync(self, tool_name: str, arguments: Dict[str, Any]) -> Dict:
        """同步直接调用工具"""
        return asyncio.run(self.call_tool_directly(tool_name, arguments))