#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AIOps Agent - 智能运维代理，整合异常检测、AI分析和自动化响应
"""

import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

from anomaly_detector import AnomalyDetector, AnomalyEvent, Severity
from vllm_client import VLLMClient, AnalysisResult
from ai_workflow_controller import AIWorkflowController


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    ANALYZING = "analyzing"
    NOTIFYING = "notifying"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class AIOpsTask:
    """AIOps 任务数据结构"""
    task_id: str
    anomaly_event: AnomalyEvent
    analysis_result: Optional[AnalysisResult]
    status: TaskStatus
    created_at: str
    updated_at: str
    notifications_sent: List[Dict[str, Any]]
    feedback: Optional[Dict[str, Any]]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['status'] = self.status.value
        data['anomaly_event'] = self.anomaly_event.to_dict()
        return data


class DataSource:
    """数据源接口"""
    
    def __init__(self, name: str, source_type: str = "database"):
        self.name = name
        self.source_type = source_type
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    def collect_metrics(self) -> Dict[str, float]:
        """采集指标数据"""
        import random
        
        return {
            "cpu_usage": random.uniform(30, 90),
            "memory_usage": random.uniform(40, 85),
            "disk_usage": random.uniform(20, 80),
            "response_time": random.uniform(100, 2000),
            "error_rate": random.uniform(0, 10),
            "request_count": random.uniform(100, 1000),
            "database_connections": random.uniform(10, 100)
        }
    
    def get_diagnostic_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """获取诊断数据"""
        return {
            "logs": ["系统运行正常", "数据库连接稳定"],
            "processes": ["nginx: 8个进程", "mysql: 2个进程"],
            "network": {"connections": 45, "bandwidth_usage": "60%"},
            "storage": {"free_space": "120GB", "io_wait": "2%"}
        }


class AIOpsAgent:
    """AIOps 智能运维代理"""
    
    def __init__(self, 
                 config_file: str = "aiops_config.json",
                 mcp_config_file: str = "mcp_config.json"):
        """初始化 AIOps Agent"""
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config = self._load_config(config_file)
        
        # 初始化组件
        self.anomaly_detector = AnomalyDetector("anomaly_config.json")
        self.vllm_client = VLLMClient(
            base_url=self.config.get("vllm_url", "http://localhost:8000/v1"),
            model_name=self.config.get("vllm_model", "qwen2.5-7b-instruct")
        )
        self.workflow_controller = AIWorkflowController(mcp_config_file)
        
        # 数据源
        self.data_sources: Dict[str, DataSource] = {}
        
        # 任务管理
        self.active_tasks: Dict[str, AIOpsTask] = {}
        self.completed_tasks: List[AIOpsTask] = []
        
        # 运行状态
        self.is_running = False
        self.monitoring_interval = self.config.get("monitoring_interval", 60)
        
        # 设置异常检测回调
        self.anomaly_detector.add_event_callback(self._on_anomaly_detected)
        
        # 添加默认数据源
        self._setup_default_data_sources()
        
        self.logger.info("🤖 AIOps Agent 初始化完成")
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            default_config = {
                "vllm_url": "http://localhost:8000/v1",
                "vllm_model": "qwen2.5-7b-instruct",
                "monitoring_interval": 60,
                "auto_analysis": True,
                "auto_notification": True,
                "notification_channels": ["tapd", "wechat"]
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            return default_config
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return {}
    
    def _setup_default_data_sources(self):
        """设置默认数据源"""
        business_db = DataSource("business_database", "database")
        self.add_data_source(business_db)
        
        system_monitor = DataSource("system_monitor", "system")
        self.add_data_source(system_monitor)
        
        app_monitor = DataSource("application_monitor", "application")
        self.add_data_source(app_monitor)
    
    def add_data_source(self, data_source: DataSource):
        """添加数据源"""
        self.data_sources[data_source.name] = data_source
        self.logger.info(f"添加数据源: {data_source.name}")
    
    def remove_data_source(self, name: str):
        """移除数据源"""
        if name in self.data_sources:
            del self.data_sources[name]
            self.logger.info(f"移除数据源: {name}")
    
    def _on_anomaly_detected(self, anomaly_event: AnomalyEvent):
        """异常检测回调"""
        self.logger.info(f"🚨 检测到异常事件: {anomaly_event.event_id}")
        
        # 创建 AIOps 任务
        task = AIOpsTask(
            task_id=str(uuid.uuid4()),
            anomaly_event=anomaly_event,
            analysis_result=None,
            status=TaskStatus.PENDING,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
            notifications_sent=[],
            feedback=None
        )
        
        # 添加到活跃任务列表
        self.active_tasks[task.task_id] = task
        
        # 异步处理任务
        asyncio.create_task(self._process_aiops_task(task))
    
    async def _process_aiops_task(self, task: AIOpsTask):
        """处理 AIOps 任务的完整流程"""
        try:
            self.logger.info(f"开始处理任务: {task.task_id}")
            
            # 1. 收集诊断数据
            diagnostic_data = await self._collect_diagnostic_data(task.anomaly_event)
            
            # 2. AI 分析
            if self.config.get("auto_analysis", True):
                task.status = TaskStatus.ANALYZING
                task.updated_at = datetime.now().isoformat()
                
                analysis_result = await self._analyze_with_ai(task.anomaly_event, diagnostic_data)
                task.analysis_result = analysis_result
            
            # 3. 发送通知
            if self.config.get("auto_notification", True):
                task.status = TaskStatus.NOTIFYING
                task.updated_at = datetime.now().isoformat()
                
                notifications = await self._send_notifications(task)
                task.notifications_sent = notifications
            
            # 4. 完成任务
            task.status = TaskStatus.COMPLETED
            task.updated_at = datetime.now().isoformat()
            
            # 移到完成列表
            self.completed_tasks.append(task)
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            self.logger.info(f"任务处理完成: {task.task_id}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败: {task.task_id}, 错误: {e}")
            task.status = TaskStatus.FAILED
            task.updated_at = datetime.now().isoformat()
    
    async def _collect_diagnostic_data(self, anomaly_event: AnomalyEvent) -> Dict[str, Any]:
        """收集诊断数据"""
        diagnostic_data = {
            "anomaly_context": anomaly_event.to_dict(),
            "timestamp": datetime.now().isoformat(),
            "sources": {}
        }
        
        for source_name, data_source in self.data_sources.items():
            try:
                source_data = data_source.get_diagnostic_data(anomaly_event.context)
                diagnostic_data["sources"][source_name] = source_data
            except Exception as e:
                diagnostic_data["sources"][source_name] = {"error": str(e)}
        
        return diagnostic_data
    
    async def _analyze_with_ai(self, anomaly_event: AnomalyEvent, diagnostic_data: Dict[str, Any]) -> AnalysisResult:
        """使用 vLLM 进行 AI 分析"""
        self.logger.info(f"开始 AI 分析: {anomaly_event.event_id}")
        
        analysis_input = {
            "anomaly": anomaly_event.to_dict(),
            "diagnostic_data": diagnostic_data,
            "context": {
                "recent_events": [event.to_dict() for event in self.anomaly_detector.get_recent_anomalies(6)]
            }
        }
        
        # 根据严重程度选择分析类型
        if anomaly_event.severity in [Severity.HIGH, Severity.CRITICAL]:
            analysis_type = "business_impact"
        else:
            analysis_type = "anomaly_analysis"
        
        try:
            analysis_result = await self.vllm_client.analyze_async(
                analysis_type=analysis_type,
                data=analysis_input,
                task_id=f"analysis_{anomaly_event.event_id}"
            )
            
            self.logger.info(f"AI 分析完成: {analysis_result.task_id}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"AI 分析失败: {e}")
            return AnalysisResult(
                task_id=f"analysis_{anomaly_event.event_id}",
                analysis_type=analysis_type,
                findings=[f"AI 分析失败: {str(e)}"],
                recommendations=["请手动检查异常情况"],
                confidence_score=0.1,
                timestamp=datetime.now().isoformat(),
                raw_response=str(e)
            )
    
    async def _send_notifications(self, task: AIOpsTask) -> List[Dict[str, Any]]:
        """发送通知"""
        notifications = []
        notification_content = self._prepare_notification_content(task)
        
        for channel in self.config.get("notification_channels", []):
            try:
                if channel == "tapd":
                    result = await self._send_tapd_notification(notification_content)
                    notifications.append({
                        "channel": "tapd",
                        "status": "success",
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                elif channel == "wechat":
                    result = await self._send_wechat_notification(notification_content)
                    notifications.append({
                        "channel": "wechat",
                        "status": "success",
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    })
                    
            except Exception as e:
                notifications.append({
                    "channel": channel,
                    "status": "failed",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
        
        return notifications
    
    def _prepare_notification_content(self, task: AIOpsTask) -> Dict[str, Any]:
        """准备通知内容"""
        anomaly = task.anomaly_event
        analysis = task.analysis_result
        
        content = {
            "title": f"🚨 系统异常警告 - {anomaly.metric_name}",
            "severity": anomaly.severity.value,
            "summary": anomaly.description,
            "details": {
                "指标名称": anomaly.metric_name,
                "当前值": anomaly.current_value,
                "异常类型": anomaly.anomaly_type.value,
                "严重程度": anomaly.severity.value,
                "检测时间": anomaly.timestamp,
                "偏差分数": f"{anomaly.deviation_score:.3f}"
            }
        }
        
        if analysis:
            content["ai_analysis"] = {
                "问题发现": analysis.findings[:3],
                "推荐方案": analysis.recommendations[:3],
                "置信度": f"{analysis.confidence_score:.3f}"
            }
        
        return content
    
    async def _send_tapd_notification(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """发送 TAPD 通知"""
        try:
            result = await asyncio.to_thread(
                self.workflow_controller.execute_workflow_sync,
                "tapd_notification",
                {"content": content}
            )
            return result
        except Exception as e:
            self.logger.error(f"TAPD 通知发送失败: {e}")
            raise
    
    async def _send_wechat_notification(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """发送微信通知"""
        try:
            wechat_message = self._format_wechat_message(content)
            
            result = await asyncio.to_thread(
                self.workflow_controller.execute_workflow_sync,
                "wechat_notification",
                {"message": wechat_message}
            )
            return result
        except Exception as e:
            self.logger.error(f"微信通知发送失败: {e}")
            raise
    
    def _format_wechat_message(self, content: Dict[str, Any]) -> str:
        """格式化微信消息"""
        message = f"{content['title']}\n\n"
        message += f"📊 异常摘要：{content['summary']}\n\n"
        
        message += "📋 详细信息：\n"
        for key, value in content['details'].items():
            message += f"• {key}：{value}\n"
        
        if 'ai_analysis' in content:
            ai_analysis = content['ai_analysis']
            message += f"\n🤖 AI 分析：\n"
            message += f"• 问题发现：{', '.join(ai_analysis['问题发现'])}\n"
            message += f"• 推荐方案：{', '.join(ai_analysis['推荐方案'])}\n"
            message += f"• 置信度：{ai_analysis['置信度']}\n"
        
        message += f"\n⏰ 时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return message
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            self.logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        self.logger.info("🎯 开始 AIOps 监控")
        
        while self.is_running:
            try:
                for source_name, data_source in self.data_sources.items():
                    try:
                        metrics = data_source.collect_metrics()
                        anomalies = self.anomaly_detector.batch_detect(metrics, source_name)
                        
                        if anomalies:
                            self.logger.info(f"从 {source_name} 检测到 {len(anomalies)} 个异常")
                    
                    except Exception as e:
                        self.logger.error(f"从数据源 {source_name} 收集数据失败: {e}")
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                await asyncio.sleep(5)
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        self.logger.info("⏹️ 停止 AIOps 监控")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id].to_dict()
        
        for task in self.completed_tasks:
            if task.task_id == task_id:
                return task.to_dict()
        
        return None
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表盘数据"""
        recent_anomalies = self.anomaly_detector.get_recent_anomalies(24)
        
        dashboard = {
            "overview": {
                "total_anomalies_24h": len(recent_anomalies),
                "critical_anomalies": len([a for a in recent_anomalies if a.severity == Severity.CRITICAL]),
                "active_tasks": len(self.active_tasks),
                "completed_tasks": len(self.completed_tasks)
            },
            "system_status": "running" if self.is_running else "stopped",
            "data_sources": list(self.data_sources.keys()),
            "recent_anomalies": [anomaly.to_dict() for anomaly in recent_anomalies[-10:]],
            "active_tasks": [task.to_dict() for task in list(self.active_tasks.values())[-5:]]
        }
        
        return dashboard


def main():
    """主函数 - 演示 AIOps Agent"""
    async def run_demo():
        agent = AIOpsAgent()
        
        print("🤖 === AIOps Agent 演示 ===")
        
        dashboard = agent.get_dashboard_data()
        print(f"系统状态: {dashboard['system_status']}")
        print(f"数据源: {dashboard['data_sources']}")
        
        print("\n📊 模拟异常数据...")
        
        anomaly = agent.anomaly_detector.detect_anomaly(
            metric_name="cpu_usage",
            value=95.0,
            source="demo"
        )
        
        if anomaly:
            print(f"✅ 触发异常: {anomaly.description}")
        
        print("⏳ 等待任务处理...")
        await asyncio.sleep(3)
        
        final_dashboard = agent.get_dashboard_data()
        print(f"\n📈 最终统计:")
        print(f"活跃任务: {final_dashboard['overview']['active_tasks']}")
        print(f"完成任务: {final_dashboard['overview']['completed_tasks']}")
        print(f"24小时异常: {final_dashboard['overview']['total_anomalies_24h']}")
    
    asyncio.run(run_demo())


if __name__ == "__main__":
    main() 