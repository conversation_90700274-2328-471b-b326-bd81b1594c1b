#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础TAPD测试脚本 - 测试最简单的工具调用
"""

import asyncio
import json
from mcp_universal_client import UniversalMCPClient


async def test_basic_tapd():
    """基础TAPD测试"""
    print("🧪 基础TAPD测试开始")
    print("=" * 50)
    
    try:
        # 创建客户端
        client = UniversalMCPClient(server_name="mcp-server-tapd")
        print("✅ TAPD客户端创建成功")
        
        # 测试1: 获取工具列表
        print("\n🔍 测试1: 获取工具列表")
        tools = await client.list_tools()
        print(f"✅ 获取到 {len(tools)} 个工具")
        
        # 测试2: 获取工作空间信息 (最简单的调用)
        print("\n🔍 测试2: 获取工作空间信息")
        try:
            result = await client.call_tool("get_workspace_info", {"workspace_id": 37936651})
            print(f"✅ 工作空间信息获取成功")
            print(f"结果类型: {type(result)}")
        except Exception as e:
            print(f"❌ 工作空间信息获取失败: {e}")
        
        # 测试3: 获取需求字段信息 (不需要创建任何东西)
        print("\n🔍 测试3: 获取需求字段信息")
        try:
            result = await client.call_tool("get_stories_fields_info", {"workspace_id": 37936651})
            print(f"✅ 需求字段信息获取成功")
            print(f"结果类型: {type(result)}")
        except Exception as e:
            print(f"❌ 需求字段信息获取失败: {e}")
        
        print("\n🎯 基础测试完成")
        
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_basic_tapd()) 