# OCR校验异常处理系统

## 📋 系统概述

OCR校验异常处理系统是一个自动化的异常处理流程，专门用于处理OCR完备性和准确性校验不通过的情况。系统通过HTTP API接收外部系统的异常告警，自动创建TAPD需求，发送企业微信通知，并持续监控处理进度。

## 🏗️ 系统架构

```
外部系统 --> HTTP API --> OCR校验处理器 --> TAPD & 企业微信
    ↓
异常识别      接收告警       自动化处理      需求管理 & 通知
    ↓            ↓              ↓              ↓
校验不通过    JSON数据     创建父子需求    责任人响应
               ↓              ↓              ↓
            存储任务       发送通知       状态监控
```

## 🚀 核心功能

### 1. 异常告警接收
- **触发方式**: HTTP API接收
- **数据格式**: JSON格式的异常告警数据
- **支持类型**: 
  - `completeness`: OCR完备性校验不通过
  - `accuracy`: OCR准确性校验不通过

### 2. 自动创建TAPD需求
- **父需求**: OCR{类型}校验不通过-{日期}
- **子需求**: 包含详细的异常信息和借据列表
- **责任人**: 根据异常类型自动分配
- **时效要求**: 48小时内处理完成

### 3. 企业微信通知
- **推送对象**: 指定责任人
- **消息内容**: 
  - 异常摘要
  - 关键数据片段
  - 处理时效要求
  - TAPD需求链接

### 4. 状态监控
- **监控周期**: 24小时
- **检查频率**: 每小时一次
- **监控内容**: TAPD需求处理状态

## 📁 文件结构

```
ops_agent2/
├── ocr_validation_handler.py          # 核心处理器
├── ocr_validation_config.json         # 系统配置
├── start_ocr_validation_system.py     # 启动脚本
├── task_monitor.py                    # 任务监控器
├── test_ocr_validation.py             # 测试脚本
├── mcp_config.json                    # MCP服务配置
└── OCR_VALIDATION_SYSTEM_README.md    # 本文档
```

## ⚙️ 配置说明

### OCR校验系统配置 (`ocr_validation_config.json`)

```json
{
  "workspace_id": 55191012,              // TAPD工作空间ID
  "responsible_persons": {
    "completeness": "产品经理",           // 完备性问题责任人
    "accuracy": "开发工程师"              // 准确性问题责任人
  },
  "wechat_webhook_url": "...",           // 企业微信Webhook URL
  "processing_deadline_hours": 48,       // 处理时效（小时）
  "check_interval_hours": 1,             // 状态检查间隔（小时）
  "check_duration_hours": 24             // 检查持续时间（小时）
}
```

### MCP服务配置 (`mcp_config.json`)

包含TAPD和企业微信的MCP服务器配置，用于调用相关API。

## 🔧 安装和部署

### 1. 环境要求
- Python 3.8+
- FastAPI
- HTTPX
- Pydantic
- MCP相关依赖

### 2. 安装步骤

```bash
# 1. 克隆项目
git clone <repository>
cd ops_agent2

# 2. 安装依赖
pip install fastapi uvicorn httpx pydantic

# 3. 配置文件
# 编辑 ocr_validation_config.json 设置工作空间ID和责任人
# 编辑 mcp_config.json 设置TAPD和企业微信API凭据

# 4. 启动系统
python start_ocr_validation_system.py
```

### 3. 启动监控器（可选）

```bash
# 启动持续监控
python task_monitor.py

# 或运行一次检查
python task_monitor.py once
```

## 📊 API接口

### 1. 接收异常告警

**端点**: `POST /api/ocr_validation_alert`

**请求体**:
```json
{
  "alert_type": "completeness",                    // 异常类型
  "documents": ["DOC001_2024_001", "DOC001_2024_002"], // 借据编号列表
  "details": "OCR识别结果缺少关键字段",              // 异常详情
  "severity": "high"                               // 严重程度
}
```

**响应**:
```json
{
  "success": true,
  "alert_id": "OCR_completeness_20241201_143000",
  "tapd_result": {...},
  "wechat_result": {...},
  "monitoring_task_id": "monitor_completeness_20241201_143000"
}
```

### 2. 查询任务状态

**端点**: `GET /api/task_status/{task_id}`

**响应**:
```json
{
  "task_id": "monitor_completeness_20241201_143000",
  "status": "monitoring",
  "next_check_time": "2024-12-01T15:30:00",
  "message": "继续监控中"
}
```

### 3. 查询活跃任务

**端点**: `GET /api/active_tasks`

**响应**:
```json
{
  "monitor_completeness_20241201_143000": {
    "status": "active",
    "start_time": "2024-12-01T14:30:00",
    "alert_type": "completeness"
  }
}
```

### 4. 健康检查

**端点**: `GET /health`

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-01T14:30:00"
}
```

## 🧪 测试方法

### 1. 自动化测试

```bash
# 运行完整测试
python test_ocr_validation.py

# 查看测试数据示例
python test_ocr_validation.py manual
```

### 2. 手动测试

使用API工具（如Postman、curl）发送测试请求：

```bash
# 测试完备性异常
curl -X POST http://localhost:8000/api/ocr_validation_alert \
  -H "Content-Type: application/json" \
  -d '{
    "alert_type": "completeness",
    "documents": ["DOC001_2024_001", "DOC001_2024_002"],
    "details": "OCR识别结果缺少关键字段",
    "severity": "high"
  }'
```

## 📈 监控和运维

### 1. 日志文件
- `ocr_validation_system.log`: 系统运行日志
- `task_monitor.log`: 任务监控日志

### 2. 监控指标
- 活跃任务数量
- 任务处理状态
- 系统健康状态
- API响应时间

### 3. 故障排除

常见问题及解决方案：

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| API调用失败 | TAPD凭据无效 | 检查mcp_config.json中的API配置 |
| 企业微信通知失败 | Webhook URL错误 | 验证企业微信机器人配置 |
| 任务状态检查失败 | 网络连接问题 | 检查网络连接和防火墙设置 |
| 需求创建失败 | 权限不足 | 确认TAPD账号有创建需求的权限 |

## 🔄 处理流程详解

### 阶段1: 异常识别
- 外部系统检测到OCR校验不通过
- 系统生成异常告警数据
- 通过HTTP API发送到处理系统

### 阶段2: 自动处理
1. **接收告警**: 验证数据格式和完整性
2. **创建需求**: 
   - 生成父需求（概述信息）
   - 生成子需求（详细信息）
3. **发送通知**: 企业微信通知责任人
4. **启动监控**: 创建24小时监控任务

### 阶段3: 状态监控
- 每小时检查TAPD需求状态
- 记录处理进度
- 任务完成或超时后停止监控

### 阶段4: 结果处理
- 需求关闭：标记任务已解决
- 监控超时：记录未处理任务
- 生成处理报告

## 🔐 安全考虑

1. **API安全**: 考虑添加认证机制
2. **数据隐私**: 敏感信息脱敏处理
3. **访问控制**: 限制API访问来源
4. **日志安全**: 避免记录敏感数据

## 🚀 扩展功能

### 未来可扩展的功能：
1. **追责机制**: 超时任务的自动升级
2. **数据分析**: 异常趋势分析和报告
3. **多渠道通知**: 邮件、短信等通知方式
4. **智能路由**: 基于异常特征的智能责任人分配
5. **API认证**: 增加Token认证机制
6. **数据持久化**: 使用数据库存储任务数据

## 📞 技术支持

如遇问题请查看：
1. 系统日志文件
2. MCP服务器连接状态
3. TAPD和企业微信API可用性
4. 网络连接和防火墙设置

---

**版本**: 1.0.0  
**最后更新**: 2024年12月1日 