#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP调试脚本 - 单独测试每个MCP服务器连接
"""

import asyncio
import logging
import traceback
from mcp_universal_client import UniversalMCPClient, MCPMultiServerClient

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_single_server(server_name: str):
    """测试单个MCP服务器"""
    print(f"\n{'='*50}")
    print(f"测试服务器: {server_name}")
    print(f"{'='*50}")
    
    try:
        client = UniversalMCPClient(server_name=server_name)
        print(f"✅ 客户端创建成功: {server_name}")
        
        # 测试工具列表
        print(f"🔍 获取工具列表...")
        tools = await client.list_tools()
        print(f"✅ 工具列表获取成功，共 {len(tools)} 个工具:")
        for tool in tools:
            print(f"   - {tool['name']}: {tool['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试服务器 {server_name} 失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def test_multi_server():
    """测试多服务器客户端"""
    print(f"\n{'='*50}")
    print(f"测试多服务器客户端")
    print(f"{'='*50}")
    
    try:
        multi_client = MCPMultiServerClient()
        print(f"✅ 多服务器客户端创建成功")
        
        # 测试获取所有工具
        print(f"🔍 获取所有服务器工具列表...")
        all_tools = await multi_client.list_all_tools()
        
        print(f"✅ 所有工具列表获取成功:")
        for server_name, tools in all_tools.items():
            print(f"   服务器 {server_name}: {len(tools)} 个工具")
            for tool in tools:
                print(f"     - {tool['name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试多服务器客户端失败: {e}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    print("🚀 开始MCP服务器调试测试")
    
    results = {}
    
    # 测试单个服务器
    server_names = ["mcp-server-tapd", "wechat-bot-mcp"]
    for server_name in server_names:
        results[server_name] = await test_single_server(server_name)
    
    # 测试多服务器
    results["multi_server"] = await test_multi_server()
    
    # 汇总结果
    print(f"\n{'='*50}")
    print(f"测试结果汇总")
    print(f"{'='*50}")
    for name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name}: {status}")

if __name__ == "__main__":
    asyncio.run(main()) 