#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR校验异常处理系统完整测试脚本
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, Any
from ocr_validation_handler import OCRValidationHandler, OCRValidationAlert


class CompleteSystemTester:
    """完整系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.handler = OCRValidationHandler()
        
    async def test_completeness_alert_flow(self) -> Dict[str, Any]:
        """测试完备性校验异常的完整流程"""
        print("=" * 60)
        print("🧪 测试完备性校验异常完整流程")
        print("=" * 60)
        
        # 创建测试告警数据
        alert = OCRValidationAlert(
            alert_type="completeness",
            documents=[
                "DOC001_2024_001",
                "DOC001_2024_002", 
                "DOC001_2024_003",
                "DOC001_2024_004",
                "DOC001_2024_005"
            ],
            details="OCR识别结果缺少关键字段，包括金额、日期、签名等重要信息。检测到的缺失字段比例超过30%，影响数据完整性。",
            severity="high"
        )
        
        print(f"📋 测试数据:")
        print(f"   异常类型: {alert.alert_type}")
        print(f"   借据数量: {len(alert.documents)}")
        print(f"   严重程度: {alert.severity}")
        print(f"   责任人: {self.handler.config.responsible_persons.get(alert.alert_type)}")
        
        try:
            print(f"\n🔄 开始处理异常告警...")
            result = await self.handler.handle_ocr_validation_alert(alert)
            
            print(f"✅ 异常处理完成!")
            print(f"   📋 告警ID: {result.get('alert_id')}")
            print(f"   📋 监控任务ID: {result.get('monitoring_task_id')}")
            
            # 检查TAPD需求创建结果
            tapd_result = result.get('tapd_result', {})
            print(f"\n📊 TAPD需求创建结果:")
            print(f"   父需求: {tapd_result.get('parent_story_name', 'N/A')}")
            print(f"   子需求: {tapd_result.get('child_story_name', 'N/A')}")
            
            # 检查企业微信通知结果
            wechat_result = result.get('wechat_result', {})
            print(f"\n📱 企业微信通知结果:")
            print(f"   发送状态: {'成功' if wechat_result.get('success') else '失败'}")
            print(f"   责任人: {wechat_result.get('responsible_person', 'N/A')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 完备性校验异常处理失败: {e}")
            return {}
    
    async def test_accuracy_alert_flow(self) -> Dict[str, Any]:
        """测试准确性校验异常的完整流程"""
        print("\n" + "=" * 60)
        print("🧪 测试准确性校验异常完整流程")
        print("=" * 60)
        
        # 创建测试告警数据
        alert = OCRValidationAlert(
            alert_type="accuracy",
            documents=[
                "DOC002_2024_001",
                "DOC002_2024_002",
                "DOC002_2024_003"
            ],
            details="OCR识别的数字金额与原始文档不符，准确率低于阈值95%。发现多处数字识别错误，如将8识别为3，将6识别为5等。",
            severity="critical"
        )
        
        print(f"📋 测试数据:")
        print(f"   异常类型: {alert.alert_type}")
        print(f"   借据数量: {len(alert.documents)}")
        print(f"   严重程度: {alert.severity}")
        print(f"   责任人: {self.handler.config.responsible_persons.get(alert.alert_type)}")
        
        try:
            print(f"\n🔄 开始处理异常告警...")
            result = await self.handler.handle_ocr_validation_alert(alert)
            
            print(f"✅ 异常处理完成!")
            print(f"   📋 告警ID: {result.get('alert_id')}")
            print(f"   📋 监控任务ID: {result.get('monitoring_task_id')}")
            
            # 检查TAPD需求创建结果
            tapd_result = result.get('tapd_result', {})
            print(f"\n📊 TAPD需求创建结果:")
            print(f"   父需求: {tapd_result.get('parent_story_name', 'N/A')}")
            print(f"   子需求: {tapd_result.get('child_story_name', 'N/A')}")
            
            # 检查企业微信通知结果
            wechat_result = result.get('wechat_result', {})
            print(f"\n📱 企业微信通知结果:")
            print(f"   发送状态: {'成功' if wechat_result.get('success') else '失败'}")
            print(f"   责任人: {wechat_result.get('responsible_person', 'N/A')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 准确性校验异常处理失败: {e}")
            return {}
    
    async def test_task_monitoring(self, task_ids: list) -> Dict[str, Any]:
        """测试任务监控功能"""
        print("\n" + "=" * 60)
        print("🧪 测试任务监控功能")
        print("=" * 60)
        
        monitoring_results = {}
        
        for task_id in task_ids:
            if not task_id:
                continue
                
            print(f"\n🔍 检查任务状态: {task_id}")
            try:
                status_result = await self.handler.check_task_status(task_id)
                
                print(f"   状态: {status_result.get('status')}")
                print(f"   消息: {status_result.get('message')}")
                if 'next_check_time' in status_result:
                    print(f"   下次检查: {status_result.get('next_check_time')}")
                
                monitoring_results[task_id] = status_result
                
            except Exception as e:
                print(f"   ❌ 状态检查失败: {e}")
                monitoring_results[task_id] = {"error": str(e)}
        
        return monitoring_results
    
    async def test_active_tasks(self) -> Dict[str, Any]:
        """测试活跃任务查询"""
        print("\n" + "=" * 60)
        print("🧪 测试活跃任务查询")
        print("=" * 60)
        
        try:
            active_tasks = self.handler.get_active_tasks()
            
            print(f"📊 活跃任务统计:")
            print(f"   总任务数: {len(active_tasks)}")
            
            for task_id, task_info in active_tasks.items():
                print(f"\n📋 任务: {task_id}")
                print(f"   类型: {task_info.get('alert_type')}")
                print(f"   状态: {task_info.get('status')}")
                print(f"   开始时间: {task_info.get('start_time')}")
                
            return active_tasks
            
        except Exception as e:
            print(f"❌ 活跃任务查询失败: {e}")
            return {}
    
    def print_test_summary(self, results: Dict[str, Any]):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结报告")
        print("=" * 60)
        
        completeness_result = results.get('completeness_test', {})
        accuracy_result = results.get('accuracy_test', {})
        monitoring_results = results.get('monitoring_results', {})
        active_tasks = results.get('active_tasks', {})
        
        print(f"📈 测试结果:")
        print(f"   完备性异常处理: {'✅ 成功' if completeness_result.get('success') else '❌ 失败'}")
        print(f"   准确性异常处理: {'✅ 成功' if accuracy_result.get('success') else '❌ 失败'}")
        print(f"   任务监控: {'✅ 成功' if monitoring_results else '❌ 失败'}")
        print(f"   活跃任务查询: {'✅ 成功' if active_tasks is not None else '❌ 失败'}")
        
        print(f"\n📋 创建的需求:")
        if completeness_result.get('tapd_result'):
            tapd_res = completeness_result['tapd_result']
            print(f"   完备性 - 父需求: {tapd_res.get('parent_story_name', 'N/A')}")
            print(f"   完备性 - 子需求: {tapd_res.get('child_story_name', 'N/A')}")
        
        if accuracy_result.get('tapd_result'):
            tapd_res = accuracy_result['tapd_result']
            print(f"   准确性 - 父需求: {tapd_res.get('parent_story_name', 'N/A')}")
            print(f"   准确性 - 子需求: {tapd_res.get('child_story_name', 'N/A')}")
        
        print(f"\n📱 企业微信通知:")
        if completeness_result.get('wechat_result'):
            wechat_res = completeness_result['wechat_result']
            print(f"   完备性通知责任人: {wechat_res.get('responsible_person', 'N/A')}")
        
        if accuracy_result.get('wechat_result'):
            wechat_res = accuracy_result['wechat_result']
            print(f"   准确性通知责任人: {wechat_res.get('responsible_person', 'N/A')}")
        
        print(f"\n🎯 监控任务:")
        for task_id, task_info in active_tasks.items():
            print(f"   {task_id}: {task_info.get('status', 'unknown')}")
        
        print(f"\n💡 操作建议:")
        print(f"   1. 请检查企业微信群是否收到通知消息")
        print(f"   2. 请登录TAPD查看创建的需求详情")
        print(f"   3. 系统将自动监控需求状态24小时")
        print(f"   4. 可运行 'python task_monitor.py' 启动定时监控")
    
    async def run_complete_test(self):
        """运行完整系统测试"""
        print("🚀 开始OCR校验异常处理系统完整测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        try:
            # 1. 测试完备性异常处理
            completeness_result = await self.test_completeness_alert_flow()
            results['completeness_test'] = completeness_result
            
            # 等待一下，避免请求过于频繁
            await asyncio.sleep(2)
            
            # 2. 测试准确性异常处理
            accuracy_result = await self.test_accuracy_alert_flow()
            results['accuracy_test'] = accuracy_result
            
            # 等待一下
            await asyncio.sleep(2)
            
            # 3. 测试任务监控
            task_ids = [
                completeness_result.get('monitoring_task_id'),
                accuracy_result.get('monitoring_task_id')
            ]
            monitoring_results = await self.test_task_monitoring(task_ids)
            results['monitoring_results'] = monitoring_results
            
            # 4. 测试活跃任务查询
            active_tasks = await self.test_active_tasks()
            results['active_tasks'] = active_tasks
            
            # 5. 打印测试总结
            self.print_test_summary(results)
            
        except Exception as e:
            print(f"\n❌ 系统测试过程中发生错误: {e}")
            
        print(f"\n🎉 OCR校验异常处理系统测试完成!")
        return results


async def main():
    """主函数"""
    tester = CompleteSystemTester()
    await tester.run_complete_test()


if __name__ == "__main__":
    asyncio.run(main()) 